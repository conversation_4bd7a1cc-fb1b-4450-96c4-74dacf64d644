'use client';

import {
  Flex,
  Image,
  Title,
  Text,
  Center,
  Anchor,
  Switch,
  useMantineColorScheme,
} from '@mantine/core';
import { IconMoon, IconSun } from '@tabler/icons-react';
import Link from 'next/link';

// Constants for paths and image sources
const DONKEY_LOGO_PATH = '/donkey-256.png';

// Helper component for navigation links
interface HeaderLinkProps {
  to: string;
  label: string;
}

function HeaderLink({ to, label }: HeaderLinkProps) {
  return (
    <Link href={to} style={{ textDecoration: 'none' }}>
      <Anchor component="span" size="sm">
        {label}
      </Anchor>
    </Link>
  );
}

export function AppHeader() {
  const { colorScheme, toggleColorScheme } = useMantineColorScheme();
  return (
    <Flex
      direction={{ base: 'column', xs: 'row' }}
      align="center"
      gap={{ base: 'sm', xs: 'lg' }}
      p="md"
      wrap={'wrap'}
      style={{ whiteSpace: 'nowrap' }}
    >
      <Flex direction="column" gap="sm">
        <Flex direction="row" align="center" gap="xs">
          <Center>
            <Image w={52} src={DONKEY_LOGO_PATH} alt="Cool donkey logo | QRCode Donkey" />
          </Center>
          <Title order={2}>QRCode Donkey</Title>
        </Flex>
      </Flex>

      <Text size="sm" c="dimmed">
        Free QR Code generator
      </Text>
      <Flex columnGap="lg" rowGap="md" wrap={'wrap'}>
        <HeaderLink to="/" label="QR Code" />
        <HeaderLink to="/paynow-qrcode" label="PayNow(SG)" />
        <HeaderLink to="/about-qrcode" label="What is a QR Code?" />
      </Flex>
      <Switch
        ml="auto"
        checked={colorScheme === 'dark'}
        onChange={() => toggleColorScheme()}
        onLabel={<IconMoon size="1rem" />}
        offLabel={<IconSun size="1rem" />}
      />
    </Flex>
  );
}
