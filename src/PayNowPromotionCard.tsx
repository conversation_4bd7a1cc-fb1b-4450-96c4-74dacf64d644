import { But<PERSON>, Card, Group, Stack, Text, Title } from '@mantine/core';
import { IconArrowRight } from '@tabler/icons-react';
import Link from 'next/link';

export const PayNowPromotionCard = () => {
  return (
    <Card
      shadow="sm"
      padding="lg"
      radius="md"
      withBorder
      style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}
    >
      <Group justify="space-between" align="center">
        <Stack gap="xs" style={{ flex: 1 }}>
          <Title order={3} c="white">
            🇸🇬 Singapore PayNow QR Codes
          </Title>
          <Text c="white" size="sm">
            Generate QR codes for Singapore PayNow payments. Support for both mobile numbers and UEN
            (business) identifiers.
          </Text>
        </Stack>
        <Link href="/paynow-qrcode" style={{ textDecoration: 'none' }}>
          <Button variant="white" rightSection={<IconArrowRight size="1rem" />}>
            Try PayNow Generator
          </Button>
        </Link>
      </Group>
    </Card>
  );
};

export default PayNowPromotionCard;
