{"c": ["webpack"], "r": ["app/about/page"], "m": ["(app-pages-browser)/./node_modules/@mantine/core/esm/components/TypographyStylesProvider/TypographyStylesProvider.mjs", "(app-pages-browser)/./node_modules/@mantine/core/esm/components/TypographyStylesProvider/TypographyStylesProvider.module.css.mjs", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fyapi%2Fcode%2Fqr-code-donkey%2Fnode_modules%2F%40mantine%2Fcore%2Fesm%2Fcomponents%2FTypographyStylesProvider%2FTypographyStylesProvider.mjs%22%2C%22ids%22%3A%5B%22TypographyStylesProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fyapi%2Fcode%2Fqr-code-donkey%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fapp-dir%2Flink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=false!"]}