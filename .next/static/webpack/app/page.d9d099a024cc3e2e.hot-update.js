"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/QRCodeGeneratorPage.tsx":
/*!*************************************!*\
  !*** ./src/QRCodeGeneratorPage.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QRCodeGeneratorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Card/Card.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Flex/Flex.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Center/Center.mjs\");\n/* harmony import */ var _components_QRCodeForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/QRCodeForm */ \"(app-pages-browser)/./src/components/QRCodeForm.tsx\");\n/* harmony import */ var _components_QRCodeDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/QRCodeDisplay */ \"(app-pages-browser)/./src/components/QRCodeDisplay.tsx\");\n/* harmony import */ var _components_QRCodeHistory__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/QRCodeHistory */ \"(app-pages-browser)/./src/components/QRCodeHistory.tsx\");\n/* harmony import */ var _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./hooks/useAnalytics */ \"(app-pages-browser)/./src/hooks/useAnalytics.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst LOCAL_STORAGE_KEY = 'qr_code_history';\nconst MAX_HISTORY_ENTRIES = 100;\nconst DEFAULT_URL = '';\nconst DEFAULT_DOT_TYPE = 'square';\nconst DEFAULT_DOT_COLOR = '#000000';\nconst DEFAULT_BACKGROUND_COLOR = '#ffffff';\nconst loadHistory = ()=>{\n    try {\n        const storedHistory = localStorage.getItem(LOCAL_STORAGE_KEY);\n        return storedHistory ? JSON.parse(storedHistory) : [];\n    } catch (error) {\n        console.error('Failed to load history from local storage:', error);\n        return [];\n    }\n};\nconst getInitialParams = ()=>{\n    if (false) {}\n    const urlParams = new URLSearchParams(window.location.search);\n    const configParam = urlParams.get('config');\n    if (configParam) {\n        try {\n            const decodedConfig = JSON.parse(atob(configParam));\n            return decodedConfig;\n        } catch (error) {\n            console.error('Failed to parse config from URL:', error);\n        }\n    }\n    return {};\n};\nfunction QRCodeGeneratorPage() {\n    _s();\n    const { url: initialUrl = DEFAULT_URL, dotType: initialDotType = DEFAULT_DOT_TYPE, dotColor: initialDotColor = DEFAULT_DOT_COLOR, backgroundColor: initialBackgroundColor = DEFAULT_BACKGROUND_COLOR } = getInitialParams();\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialUrl);\n    const [dotType, setDotType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialDotType);\n    const [dotColor, setDotColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialDotColor);\n    const [backgroundColor, setBackgroundColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialBackgroundColor);\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const qrCodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { trackQRCodeGeneration, trackEngagement, trackQRCodeDownload } = (0,_hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_5__.useAnalytics)();\n    const saveHistory = (newHistory)=>{\n        try {\n            localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(newHistory));\n        } catch (error) {\n            console.error('Failed to save history to local storage:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QRCodeGeneratorPage.useEffect\": ()=>{\n            setHistory(loadHistory());\n        }\n    }[\"QRCodeGeneratorPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QRCodeGeneratorPage.useEffect\": ()=>{\n            const urlParams = new URLSearchParams(window.location.search);\n            const config = {\n                url,\n                dotType,\n                dotColor,\n                backgroundColor\n            };\n            const isDefaultConfig = url === DEFAULT_URL && dotType === DEFAULT_DOT_TYPE && dotColor === DEFAULT_DOT_COLOR && backgroundColor === DEFAULT_BACKGROUND_COLOR;\n            if (isDefaultConfig) {\n                urlParams.delete('config');\n            } else {\n                const encodedConfig = btoa(JSON.stringify(config));\n                urlParams.set('config', encodedConfig);\n            }\n            // These parameters are now handled by the 'config' parameter, so they should always be deleted.\n            urlParams.delete('dotType');\n            urlParams.delete('dotColor');\n            urlParams.delete('backgroundColor');\n            window.history.replaceState({}, '', \"\".concat(window.location.pathname).concat(isDefaultConfig ? '' : '?').concat(urlParams));\n        }\n    }[\"QRCodeGeneratorPage.useEffect\"], [\n        url,\n        dotType,\n        dotColor,\n        backgroundColor\n    ]);\n    const handleGenerateQRCode = async (values)=>{\n        setUrl(values.url);\n        setDotType(values.dotType);\n        setDotColor(values.dotColor);\n        setBackgroundColor(values.backgroundColor);\n        // Track QR code generation\n        trackQRCodeGeneration(values.url, values.dotType);\n        const newEntry = {\n            url: values.url,\n            dotType: values.dotType,\n            dotColor: values.dotColor,\n            backgroundColor: values.backgroundColor,\n            timestamp: Date.now()\n        };\n        setHistory((prevHistory)=>{\n            const updatedHistory = [\n                newEntry,\n                ...prevHistory\n            ].slice(0, MAX_HISTORY_ENTRIES);\n            saveHistory(updatedHistory);\n            return updatedHistory;\n        });\n    };\n    const handleLoadHistoryEntry = (entry)=>{\n        setUrl(entry.url);\n        setDotType(entry.dotType);\n        setDotColor(entry.dotColor);\n        setBackgroundColor(entry.backgroundColor);\n        // Track history entry load\n        trackEngagement('load_history_entry', entry.dotType);\n    };\n    const handleDeleteHistoryEntry = (index)=>{\n        const updatedHistory = history.filter((_, i)=>i !== index);\n        setHistory(updatedHistory);\n        saveHistory(updatedHistory);\n        // Track history entry deletion\n        trackEngagement('delete_history_entry');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Stack, {\n        gap: \"xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Title, {\n                order: 1,\n                children: \"QR Code Generator\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Grid, {\n                gutter: \"xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Grid.Col, {\n                        span: {\n                            sm: 12,\n                            md: 6,\n                            lg: 8\n                        },\n                        order: {\n                            base: 2,\n                            md: 1\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            shadow: \"sm\",\n                            padding: \"lg\",\n                            radius: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Stack, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QRCodeForm__WEBPACK_IMPORTED_MODULE_2__.QRCodeForm, {\n                                        initialUrl: url,\n                                        initialDotType: dotType,\n                                        initialDotColor: dotColor,\n                                        initialBackgroundColor: backgroundColor,\n                                        onGenerate: handleGenerateQRCode,\n                                        onDotTypeChange: setDotType,\n                                        onDotColorChange: setDotColor,\n                                        onBackgroundColorChange: setBackgroundColor\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Flex, {\n                                        gap: \"md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                style: {\n                                                    flexGrow: 1\n                                                },\n                                                size: \"md\",\n                                                variant: \"light\",\n                                                onClick: ()=>{\n                                                    window.open(url, '_blank');\n                                                },\n                                                children: \"Open URL\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                style: {\n                                                    flexGrow: 1\n                                                },\n                                                size: \"md\",\n                                                variant: \"light\",\n                                                onClick: ()=>{\n                                                    if (qrCodeRef.current) {\n                                                        qrCodeRef.current.download({\n                                                            name: 'qrcode',\n                                                            extension: 'png'\n                                                        });\n                                                        trackQRCodeDownload('png');\n                                                    }\n                                                },\n                                                children: \"Download QR Code\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                style: {\n                                                    flexGrow: 1\n                                                },\n                                                size: \"md\",\n                                                color: \"pink\",\n                                                variant: \"light\",\n                                                onClick: ()=>{\n                                                    // set options to default\n                                                    setUrl(DEFAULT_URL);\n                                                    setDotType(DEFAULT_DOT_TYPE);\n                                                    setDotColor(DEFAULT_DOT_COLOR);\n                                                    setBackgroundColor(DEFAULT_BACKGROUND_COLOR);\n                                                },\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Grid.Col, {\n                        span: {\n                            sm: 12,\n                            md: 6,\n                            lg: 4\n                        },\n                        order: {\n                            base: 1,\n                            md: 2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            shadow: \"sm\",\n                            padding: \"lg\",\n                            radius: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Center, {\n                                h: \"100%\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QRCodeDisplay__WEBPACK_IMPORTED_MODULE_3__.QRCodeDisplay, {\n                                    data: url || 'https://www.qrcode-donkey.com',\n                                    dotType: dotType,\n                                    dotColor: dotColor,\n                                    backgroundColor: backgroundColor,\n                                    qrCodeRef: qrCodeRef\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QRCodeHistory__WEBPACK_IMPORTED_MODULE_4__.QRCodeHistory, {\n                history: history,\n                onLoadHistoryEntry: handleLoadHistoryEntry,\n                onDeleteHistoryEntry: handleDeleteHistoryEntry\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, this);\n}\n_s(QRCodeGeneratorPage, \"GZoS88CFq3mT6V1JLoHR1P2AGLc=\", false, function() {\n    return [\n        _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_5__.useAnalytics\n    ];\n});\n_c = QRCodeGeneratorPage;\nvar _c;\n$RefreshReg$(_c, \"QRCodeGeneratorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/QRCodeGeneratorPage.tsx\n"));

/***/ })

});