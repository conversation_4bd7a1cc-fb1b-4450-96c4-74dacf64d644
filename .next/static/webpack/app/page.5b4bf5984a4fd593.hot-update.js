"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/QRCodeGeneratorPage.tsx":
/*!*************************************!*\
  !*** ./src/QRCodeGeneratorPage.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QRCodeGeneratorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Flex/Flex.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Card/Card.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Center/Center.mjs\");\n/* harmony import */ var _components_QRCodeForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/QRCodeForm */ \"(app-pages-browser)/./src/components/QRCodeForm.tsx\");\n/* harmony import */ var _components_QRCodeDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/QRCodeDisplay */ \"(app-pages-browser)/./src/components/QRCodeDisplay.tsx\");\n/* harmony import */ var _components_QRCodeHistory__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/QRCodeHistory */ \"(app-pages-browser)/./src/components/QRCodeHistory.tsx\");\n/* harmony import */ var _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./hooks/useAnalytics */ \"(app-pages-browser)/./src/hooks/useAnalytics.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst LOCAL_STORAGE_KEY = 'qr_code_history';\nconst MAX_HISTORY_ENTRIES = 100;\nconst DEFAULT_URL = '';\nconst DEFAULT_DOT_TYPE = 'square';\nconst DEFAULT_DOT_COLOR = '#000000';\nconst DEFAULT_BACKGROUND_COLOR = '#ffffff';\nconst loadHistory = ()=>{\n    try {\n        const storedHistory = localStorage.getItem(LOCAL_STORAGE_KEY);\n        return storedHistory ? JSON.parse(storedHistory) : [];\n    } catch (error) {\n        console.error('Failed to load history from local storage:', error);\n        return [];\n    }\n};\nconst getInitialParams = ()=>{\n    if (false) {}\n    const urlParams = new URLSearchParams(window.location.search);\n    const configParam = urlParams.get('config');\n    if (configParam) {\n        try {\n            const decodedConfig = JSON.parse(atob(configParam));\n            return decodedConfig;\n        } catch (error) {\n            console.error('Failed to parse config from URL:', error);\n        }\n    }\n    return {};\n};\nfunction QRCodeGeneratorPage() {\n    _s();\n    const { url: initialUrl = DEFAULT_URL, dotType: initialDotType = DEFAULT_DOT_TYPE, dotColor: initialDotColor = DEFAULT_DOT_COLOR, backgroundColor: initialBackgroundColor = DEFAULT_BACKGROUND_COLOR } = getInitialParams();\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialUrl);\n    const [dotType, setDotType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialDotType);\n    const [dotColor, setDotColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialDotColor);\n    const [backgroundColor, setBackgroundColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialBackgroundColor);\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const qrCodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { trackQRCodeGeneration, trackEngagement, trackQRCodeDownload } = (0,_hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_5__.useAnalytics)();\n    const saveHistory = (newHistory)=>{\n        try {\n            localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(newHistory));\n        } catch (error) {\n            console.error('Failed to save history to local storage:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QRCodeGeneratorPage.useEffect\": ()=>{\n            setHistory(loadHistory());\n        }\n    }[\"QRCodeGeneratorPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QRCodeGeneratorPage.useEffect\": ()=>{\n            const urlParams = new URLSearchParams(window.location.search);\n            const config = {\n                url,\n                dotType,\n                dotColor,\n                backgroundColor\n            };\n            const isDefaultConfig = url === DEFAULT_URL && dotType === DEFAULT_DOT_TYPE && dotColor === DEFAULT_DOT_COLOR && backgroundColor === DEFAULT_BACKGROUND_COLOR;\n            if (isDefaultConfig) {\n                urlParams.delete('config');\n            } else {\n                const encodedConfig = btoa(JSON.stringify(config));\n                urlParams.set('config', encodedConfig);\n            }\n            // These parameters are now handled by the 'config' parameter, so they should always be deleted.\n            urlParams.delete('dotType');\n            urlParams.delete('dotColor');\n            urlParams.delete('backgroundColor');\n            window.history.replaceState({}, '', \"\".concat(window.location.pathname).concat(isDefaultConfig ? '' : '?').concat(urlParams));\n        }\n    }[\"QRCodeGeneratorPage.useEffect\"], [\n        url,\n        dotType,\n        dotColor,\n        backgroundColor\n    ]);\n    const handleGenerateQRCode = async (values)=>{\n        setUrl(values.url);\n        setDotType(values.dotType);\n        setDotColor(values.dotColor);\n        setBackgroundColor(values.backgroundColor);\n        // Track QR code generation\n        trackQRCodeGeneration(values.url, values.dotType);\n        const newEntry = {\n            url: values.url,\n            dotType: values.dotType,\n            dotColor: values.dotColor,\n            backgroundColor: values.backgroundColor,\n            timestamp: Date.now()\n        };\n        setHistory((prevHistory)=>{\n            const updatedHistory = [\n                newEntry,\n                ...prevHistory\n            ].slice(0, MAX_HISTORY_ENTRIES);\n            saveHistory(updatedHistory);\n            return updatedHistory;\n        });\n    };\n    const handleLoadHistoryEntry = (entry)=>{\n        setUrl(entry.url);\n        setDotType(entry.dotType);\n        setDotColor(entry.dotColor);\n        setBackgroundColor(entry.backgroundColor);\n        // Track history entry load\n        trackEngagement('load_history_entry', entry.dotType);\n    };\n    const handleDeleteHistoryEntry = (index)=>{\n        const updatedHistory = history.filter((_, i)=>i !== index);\n        setHistory(updatedHistory);\n        saveHistory(updatedHistory);\n        // Track history entry deletion\n        trackEngagement('delete_history_entry');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Stack, {\n        gap: \"xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Stack, {\n                gap: \"sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Title, {\n                    order: 1,\n                    children: \"QR Code Generator\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Flex, {\n                gap: \"md\",\n                justify: \"space-between\",\n                align: \"flex-start\",\n                wrap: \"wrap\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Stack, {\n                        gap: \"xl\",\n                        style: {\n                            flexGrow: 1\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Grid, {\n                            gutter: \"xl\",\n                            breakpoints: {\n                                xs: '320px',\n                                sm: '640px',\n                                md: '768px',\n                                lg: '1024px',\n                                xl: '1200px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Grid.Col, {\n                                    span: {\n                                        sm: 12,\n                                        md: 6,\n                                        lg: 8\n                                    },\n                                    order: {\n                                        base: 2,\n                                        md: 1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                                        shadow: \"sm\",\n                                        padding: \"lg\",\n                                        radius: \"md\",\n                                        withBorder: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Stack, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QRCodeForm__WEBPACK_IMPORTED_MODULE_2__.QRCodeForm, {\n                                                    initialUrl: url,\n                                                    initialDotType: dotType,\n                                                    initialDotColor: dotColor,\n                                                    initialBackgroundColor: backgroundColor,\n                                                    onGenerate: handleGenerateQRCode,\n                                                    onDotTypeChange: setDotType,\n                                                    onDotColorChange: setDotColor,\n                                                    onBackgroundColorChange: setBackgroundColor\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Flex, {\n                                                    gap: \"md\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            style: {\n                                                                flexGrow: 1\n                                                            },\n                                                            size: \"md\",\n                                                            variant: \"light\",\n                                                            onClick: ()=>{\n                                                                window.open(url, '_blank');\n                                                            },\n                                                            children: \"Open URL\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            style: {\n                                                                flexGrow: 1\n                                                            },\n                                                            size: \"md\",\n                                                            variant: \"light\",\n                                                            onClick: ()=>{\n                                                                if (qrCodeRef.current) {\n                                                                    qrCodeRef.current.download({\n                                                                        name: 'qrcode',\n                                                                        extension: 'png'\n                                                                    });\n                                                                    trackQRCodeDownload('png');\n                                                                }\n                                                            },\n                                                            children: \"Download QR Code\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            style: {\n                                                                flexGrow: 1\n                                                            },\n                                                            size: \"md\",\n                                                            color: \"pink\",\n                                                            variant: \"light\",\n                                                            onClick: ()=>{\n                                                                // set options to default\n                                                                setUrl(DEFAULT_URL);\n                                                                setDotType(DEFAULT_DOT_TYPE);\n                                                                setDotColor(DEFAULT_DOT_COLOR);\n                                                                setBackgroundColor(DEFAULT_BACKGROUND_COLOR);\n                                                            },\n                                                            children: \"Clear\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Grid.Col, {\n                                    span: {\n                                        sm: 12,\n                                        md: 6,\n                                        lg: 4\n                                    },\n                                    order: {\n                                        base: 1,\n                                        md: 2\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Center, {\n                                        h: \"100%\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QRCodeDisplay__WEBPACK_IMPORTED_MODULE_3__.QRCodeDisplay, {\n                                            data: url || 'https://www.qrcode-donkey.com',\n                                            dotType: dotType,\n                                            dotColor: dotColor,\n                                            backgroundColor: backgroundColor,\n                                            qrCodeRef: qrCodeRef\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Stack, {\n                        w: \"100%\",\n                        gap: \"md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QRCodeHistory__WEBPACK_IMPORTED_MODULE_4__.QRCodeHistory, {\n                            history: history,\n                            onLoadHistoryEntry: handleLoadHistoryEntry,\n                            onDeleteHistoryEntry: handleDeleteHistoryEntry\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, this);\n}\n_s(QRCodeGeneratorPage, \"GZoS88CFq3mT6V1JLoHR1P2AGLc=\", false, function() {\n    return [\n        _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_5__.useAnalytics\n    ];\n});\n_c = QRCodeGeneratorPage;\nvar _c;\n$RefreshReg$(_c, \"QRCodeGeneratorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/QRCodeGeneratorPage.tsx\n"));

/***/ })

});