"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/AppHeader.tsx":
/*!***************************!*\
  !*** ./src/AppHeader.tsx ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppHeader: () => (/* binding */ AppHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Anchor,Center,Flex,Image,Switch,Text,Title,useMantineColorScheme!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Anchor/Anchor.mjs\");\n/* harmony import */ var _barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Anchor,Center,Flex,Image,Switch,Text,Title,useMantineColorScheme!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/core/MantineProvider/use-mantine-color-scheme/use-mantine-color-scheme.mjs\");\n/* harmony import */ var _barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Anchor,Center,Flex,Image,Switch,Text,Title,useMantineColorScheme!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Flex/Flex.mjs\");\n/* harmony import */ var _barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Anchor,Center,Flex,Image,Switch,Text,Title,useMantineColorScheme!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Center/Center.mjs\");\n/* harmony import */ var _barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Anchor,Center,Flex,Image,Switch,Text,Title,useMantineColorScheme!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Image/Image.mjs\");\n/* harmony import */ var _barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Anchor,Center,Flex,Image,Switch,Text,Title,useMantineColorScheme!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Anchor,Center,Flex,Image,Switch,Text,Title,useMantineColorScheme!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Text/Text.mjs\");\n/* harmony import */ var _barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Anchor,Center,Flex,Image,Switch,Text,Title,useMantineColorScheme!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Switch/Switch.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconMoon_IconSun_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=IconMoon,IconSun!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconMoon.mjs\");\n/* harmony import */ var _barrel_optimize_names_IconMoon_IconSun_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=IconMoon,IconSun!=!@tabler/icons-react */ \"(app-pages-browser)/./node_modules/@tabler/icons-react/dist/esm/icons/IconSun.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AppHeader auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Constants for paths and image sources\nconst DONKEY_LOGO_PATH = '/donkey-256.png';\nfunction HeaderLink(param) {\n    let { to, label } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n        href: to,\n        style: {\n            textDecoration: 'none'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_2__.Anchor, {\n            component: \"span\",\n            size: \"sm\",\n            children: label\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/code/qr-code-donkey/src/AppHeader.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/code/qr-code-donkey/src/AppHeader.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n_c = HeaderLink;\nfunction AppHeader() {\n    _s();\n    const { colorScheme, toggleColorScheme } = (0,_barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_3__.useMantineColorScheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n        direction: {\n            base: 'column',\n            xs: 'row'\n        },\n        align: \"center\",\n        gap: {\n            base: 'sm',\n            xs: 'lg'\n        },\n        p: \"md\",\n        wrap: 'wrap',\n        style: {\n            whiteSpace: 'nowrap'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n                direction: \"column\",\n                gap: \"sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n                    direction: \"row\",\n                    align: \"center\",\n                    gap: \"xs\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_5__.Center, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                w: 52,\n                                src: DONKEY_LOGO_PATH,\n                                alt: \"Cool donkey logo | QRCode Donkey\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/AppHeader.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/code/qr-code-donkey/src/AppHeader.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Title, {\n                            order: 2,\n                            children: \"QRCode Donkey\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/code/qr-code-donkey/src/AppHeader.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/code/qr-code-donkey/src/AppHeader.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/code/qr-code-donkey/src/AppHeader.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                size: \"sm\",\n                c: \"dimmed\",\n                children: \"Free QR Code generator\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/code/qr-code-donkey/src/AppHeader.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n                columnGap: \"lg\",\n                rowGap: \"md\",\n                wrap: 'wrap',\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderLink, {\n                        to: \"/\",\n                        label: \"QR Code\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/AppHeader.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderLink, {\n                        to: \"/paynow-qrcode\",\n                        label: \"PayNow(SG)\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/AppHeader.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeaderLink, {\n                        to: \"/about-qrcode\",\n                        label: \"What is a QR Code?\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/AppHeader.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/code/qr-code-donkey/src/AppHeader.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                ml: \"auto\",\n                checked: colorScheme === 'dark',\n                onChange: ()=>toggleColorScheme(),\n                onLabel: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconMoon_IconSun_tabler_icons_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: \"1rem\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/code/qr-code-donkey/src/AppHeader.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 18\n                }, void 0),\n                offLabel: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_IconMoon_IconSun_tabler_icons_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: \"1rem\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/code/qr-code-donkey/src/AppHeader.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 19\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/code/qr-code-donkey/src/AppHeader.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/code/qr-code-donkey/src/AppHeader.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_s(AppHeader, \"kvioy2X7rbEIvARlMnVlktktibE=\", false, function() {\n    return [\n        _barrel_optimize_names_Anchor_Center_Flex_Image_Switch_Text_Title_useMantineColorScheme_mantine_core__WEBPACK_IMPORTED_MODULE_3__.useMantineColorScheme\n    ];\n});\n_c1 = AppHeader;\nvar _c, _c1;\n$RefreshReg$(_c, \"HeaderLink\");\n$RefreshReg$(_c1, \"AppHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/AppHeader.tsx\n"));

/***/ })

});