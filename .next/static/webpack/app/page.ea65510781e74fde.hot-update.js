"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/QRCodeGeneratorPage.tsx":
/*!*************************************!*\
  !*** ./src/QRCodeGeneratorPage.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QRCodeGeneratorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Card/Card.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Flex/Flex.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Center/Center.mjs\");\n/* harmony import */ var _components_QRCodeForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/QRCodeForm */ \"(app-pages-browser)/./src/components/QRCodeForm.tsx\");\n/* harmony import */ var _components_QRCodeDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/QRCodeDisplay */ \"(app-pages-browser)/./src/components/QRCodeDisplay.tsx\");\n/* harmony import */ var _components_QRCodeHistory__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/QRCodeHistory */ \"(app-pages-browser)/./src/components/QRCodeHistory.tsx\");\n/* harmony import */ var _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./hooks/useAnalytics */ \"(app-pages-browser)/./src/hooks/useAnalytics.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst LOCAL_STORAGE_KEY = 'qr_code_history';\nconst MAX_HISTORY_ENTRIES = 100;\nconst DEFAULT_URL = '';\nconst DEFAULT_DOT_TYPE = 'square';\nconst DEFAULT_DOT_COLOR = '#000000';\nconst DEFAULT_BACKGROUND_COLOR = '#ffffff';\nconst loadHistory = ()=>{\n    try {\n        const storedHistory = localStorage.getItem(LOCAL_STORAGE_KEY);\n        return storedHistory ? JSON.parse(storedHistory) : [];\n    } catch (error) {\n        console.error('Failed to load history from local storage:', error);\n        return [];\n    }\n};\nconst getInitialParams = ()=>{\n    if (false) {}\n    const urlParams = new URLSearchParams(window.location.search);\n    const configParam = urlParams.get('config');\n    if (configParam) {\n        try {\n            const decodedConfig = JSON.parse(atob(configParam));\n            return decodedConfig;\n        } catch (error) {\n            console.error('Failed to parse config from URL:', error);\n        }\n    }\n    return {};\n};\nfunction QRCodeGeneratorPage() {\n    _s();\n    const { url: initialUrl = DEFAULT_URL, dotType: initialDotType = DEFAULT_DOT_TYPE, dotColor: initialDotColor = DEFAULT_DOT_COLOR, backgroundColor: initialBackgroundColor = DEFAULT_BACKGROUND_COLOR } = getInitialParams();\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialUrl);\n    const [dotType, setDotType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialDotType);\n    const [dotColor, setDotColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialDotColor);\n    const [backgroundColor, setBackgroundColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialBackgroundColor);\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const qrCodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { trackQRCodeGeneration, trackEngagement, trackQRCodeDownload } = (0,_hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_5__.useAnalytics)();\n    const saveHistory = (newHistory)=>{\n        try {\n            localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(newHistory));\n        } catch (error) {\n            console.error('Failed to save history to local storage:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QRCodeGeneratorPage.useEffect\": ()=>{\n            setHistory(loadHistory());\n        }\n    }[\"QRCodeGeneratorPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QRCodeGeneratorPage.useEffect\": ()=>{\n            const urlParams = new URLSearchParams(window.location.search);\n            const config = {\n                url,\n                dotType,\n                dotColor,\n                backgroundColor\n            };\n            const isDefaultConfig = url === DEFAULT_URL && dotType === DEFAULT_DOT_TYPE && dotColor === DEFAULT_DOT_COLOR && backgroundColor === DEFAULT_BACKGROUND_COLOR;\n            if (isDefaultConfig) {\n                urlParams.delete('config');\n            } else {\n                const encodedConfig = btoa(JSON.stringify(config));\n                urlParams.set('config', encodedConfig);\n            }\n            // These parameters are now handled by the 'config' parameter, so they should always be deleted.\n            urlParams.delete('dotType');\n            urlParams.delete('dotColor');\n            urlParams.delete('backgroundColor');\n            window.history.replaceState({}, '', \"\".concat(window.location.pathname).concat(isDefaultConfig ? '' : '?').concat(urlParams));\n        }\n    }[\"QRCodeGeneratorPage.useEffect\"], [\n        url,\n        dotType,\n        dotColor,\n        backgroundColor\n    ]);\n    const handleGenerateQRCode = async (values)=>{\n        setUrl(values.url);\n        setDotType(values.dotType);\n        setDotColor(values.dotColor);\n        setBackgroundColor(values.backgroundColor);\n        // Track QR code generation\n        trackQRCodeGeneration(values.url, values.dotType);\n        const newEntry = {\n            url: values.url,\n            dotType: values.dotType,\n            dotColor: values.dotColor,\n            backgroundColor: values.backgroundColor,\n            timestamp: Date.now()\n        };\n        setHistory((prevHistory)=>{\n            const updatedHistory = [\n                newEntry,\n                ...prevHistory\n            ].slice(0, MAX_HISTORY_ENTRIES);\n            saveHistory(updatedHistory);\n            return updatedHistory;\n        });\n    };\n    const handleLoadHistoryEntry = (entry)=>{\n        setUrl(entry.url);\n        setDotType(entry.dotType);\n        setDotColor(entry.dotColor);\n        setBackgroundColor(entry.backgroundColor);\n        // Track history entry load\n        trackEngagement('load_history_entry', entry.dotType);\n    };\n    const handleDeleteHistoryEntry = (index)=>{\n        const updatedHistory = history.filter((_, i)=>i !== index);\n        setHistory(updatedHistory);\n        saveHistory(updatedHistory);\n        // Track history entry deletion\n        trackEngagement('delete_history_entry');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Stack, {\n        gap: \"xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Title, {\n                order: 1,\n                children: \"QR Code Generator\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Grid, {\n                gutter: \"xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Grid.Col, {\n                        span: {\n                            sm: 12,\n                            md: 6,\n                            lg: 8\n                        },\n                        order: {\n                            base: 2,\n                            md: 1\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            shadow: \"sm\",\n                            padding: \"lg\",\n                            radius: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Stack, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QRCodeForm__WEBPACK_IMPORTED_MODULE_2__.QRCodeForm, {\n                                        initialUrl: url,\n                                        initialDotType: dotType,\n                                        initialDotColor: dotColor,\n                                        initialBackgroundColor: backgroundColor,\n                                        onGenerate: handleGenerateQRCode,\n                                        onDotTypeChange: setDotType,\n                                        onDotColorChange: setDotColor,\n                                        onBackgroundColorChange: setBackgroundColor\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Flex, {\n                                        gap: \"md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                style: {\n                                                    flexGrow: 1\n                                                },\n                                                size: \"md\",\n                                                variant: \"light\",\n                                                onClick: ()=>{\n                                                    window.open(url, '_blank');\n                                                },\n                                                children: \"Open URL\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                style: {\n                                                    flexGrow: 1\n                                                },\n                                                size: \"md\",\n                                                variant: \"light\",\n                                                onClick: ()=>{\n                                                    if (qrCodeRef.current) {\n                                                        qrCodeRef.current.download({\n                                                            name: 'qrcode',\n                                                            extension: 'png'\n                                                        });\n                                                        trackQRCodeDownload('png');\n                                                    }\n                                                },\n                                                children: \"Download QR Code\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                style: {\n                                                    flexGrow: 1\n                                                },\n                                                size: \"md\",\n                                                color: \"pink\",\n                                                variant: \"light\",\n                                                onClick: ()=>{\n                                                    // set options to default\n                                                    setUrl(DEFAULT_URL);\n                                                    setDotType(DEFAULT_DOT_TYPE);\n                                                    setDotColor(DEFAULT_DOT_COLOR);\n                                                    setBackgroundColor(DEFAULT_BACKGROUND_COLOR);\n                                                },\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Grid.Col, {\n                        span: {\n                            sm: 12,\n                            md: 6,\n                            lg: 4\n                        },\n                        order: {\n                            base: 1,\n                            md: 2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Center, {\n                            h: \"100%\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QRCodeDisplay__WEBPACK_IMPORTED_MODULE_3__.QRCodeDisplay, {\n                                data: url || 'https://www.qrcode-donkey.com',\n                                dotType: dotType,\n                                dotColor: dotColor,\n                                backgroundColor: backgroundColor,\n                                qrCodeRef: qrCodeRef\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QRCodeHistory__WEBPACK_IMPORTED_MODULE_4__.QRCodeHistory, {\n                history: history,\n                onLoadHistoryEntry: handleLoadHistoryEntry,\n                onDeleteHistoryEntry: handleDeleteHistoryEntry\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, this);\n}\n_s(QRCodeGeneratorPage, \"GZoS88CFq3mT6V1JLoHR1P2AGLc=\", false, function() {\n    return [\n        _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_5__.useAnalytics\n    ];\n});\n_c = QRCodeGeneratorPage;\nvar _c;\n$RefreshReg$(_c, \"QRCodeGeneratorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9RUkNvZGVHZW5lcmF0b3JQYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNvRDtBQUMyQjtBQUcxQjtBQUNNO0FBQ0E7QUFDUDtBQVdwRCxNQUFNYyxvQkFBb0I7QUFDMUIsTUFBTUMsc0JBQXNCO0FBRTVCLE1BQU1DLGNBQWM7QUFDcEIsTUFBTUMsbUJBQTRCO0FBQ2xDLE1BQU1DLG9CQUFvQjtBQUMxQixNQUFNQywyQkFBMkI7QUFFakMsTUFBTUMsY0FBYztJQUNsQixJQUFJO1FBQ0YsTUFBTUMsZ0JBQWdCQyxhQUFhQyxPQUFPLENBQUNUO1FBQzNDLE9BQU9PLGdCQUFnQkcsS0FBS0MsS0FBSyxDQUFDSixpQkFBaUIsRUFBRTtJQUN2RCxFQUFFLE9BQU9LLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDhDQUE4Q0E7UUFDNUQsT0FBTyxFQUFFO0lBQ1g7QUFDRjtBQUVBLE1BQU1FLG1CQUFtQjtJQUN2QixJQUFJLEtBQTZCLEVBQUUsRUFBVTtJQUM3QyxNQUFNQyxZQUFZLElBQUlDLGdCQUFnQkMsT0FBT0MsUUFBUSxDQUFDQyxNQUFNO0lBQzVELE1BQU1DLGNBQWNMLFVBQVVNLEdBQUcsQ0FBQztJQUNsQyxJQUFJRCxhQUFhO1FBQ2YsSUFBSTtZQUNGLE1BQU1FLGdCQUFnQlosS0FBS0MsS0FBSyxDQUFDWSxLQUFLSDtZQUN0QyxPQUFPRTtRQUNULEVBQUUsT0FBT1YsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsb0NBQW9DQTtRQUNwRDtJQUNGO0lBQ0EsT0FBTyxDQUFDO0FBQ1Y7QUFFZSxTQUFTWTs7SUFDdEIsTUFBTSxFQUNKQyxLQUFLQyxhQUFheEIsV0FBVyxFQUM3QnlCLFNBQVNDLGlCQUFpQnpCLGdCQUFnQixFQUMxQzBCLFVBQVVDLGtCQUFrQjFCLGlCQUFpQixFQUM3QzJCLGlCQUFpQkMseUJBQXlCM0Isd0JBQXdCLEVBQ25FLEdBQUdTO0lBQ0osTUFBTSxDQUFDVyxLQUFLUSxPQUFPLEdBQUc5QywrQ0FBUUEsQ0FBQ3VDO0lBQy9CLE1BQU0sQ0FBQ0MsU0FBU08sV0FBVyxHQUFHL0MsK0NBQVFBLENBQVV5QztJQUNoRCxNQUFNLENBQUNDLFVBQVVNLFlBQVksR0FBR2hELCtDQUFRQSxDQUFDMkM7SUFDekMsTUFBTSxDQUFDQyxpQkFBaUJLLG1CQUFtQixHQUFHakQsK0NBQVFBLENBQUM2QztJQUN2RCxNQUFNLENBQUNLLFNBQVNDLFdBQVcsR0FBR25ELCtDQUFRQSxDQUFpQixFQUFFO0lBQ3pELE1BQU1vRCxZQUFZbkQsNkNBQU1BLENBQXVCO0lBRS9DLE1BQU0sRUFBRW9ELHFCQUFxQixFQUFFQyxlQUFlLEVBQUVDLG1CQUFtQixFQUFFLEdBQUczQyxpRUFBWUE7SUFFcEYsTUFBTTRDLGNBQWMsQ0FBQ0M7UUFDbkIsSUFBSTtZQUNGcEMsYUFBYXFDLE9BQU8sQ0FBQzdDLG1CQUFtQlUsS0FBS29DLFNBQVMsQ0FBQ0Y7UUFDekQsRUFBRSxPQUFPaEMsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNENBQTRDQTtRQUM1RDtJQUNGO0lBRUExQixnREFBU0E7eUNBQUM7WUFDUm9ELFdBQVdoQztRQUNiO3dDQUFHLEVBQUU7SUFFTHBCLGdEQUFTQTt5Q0FBQztZQUNSLE1BQU02QixZQUFZLElBQUlDLGdCQUFnQkMsT0FBT0MsUUFBUSxDQUFDQyxNQUFNO1lBQzVELE1BQU00QixTQUFTO2dCQUFFdEI7Z0JBQUtFO2dCQUFTRTtnQkFBVUU7WUFBZ0I7WUFFekQsTUFBTWlCLGtCQUNKdkIsUUFBUXZCLGVBQ1J5QixZQUFZeEIsb0JBQ1owQixhQUFhekIscUJBQ2IyQixvQkFBb0IxQjtZQUV0QixJQUFJMkMsaUJBQWlCO2dCQUNuQmpDLFVBQVVrQyxNQUFNLENBQUM7WUFDbkIsT0FBTztnQkFDTCxNQUFNQyxnQkFBZ0JDLEtBQUt6QyxLQUFLb0MsU0FBUyxDQUFDQztnQkFDMUNoQyxVQUFVcUMsR0FBRyxDQUFDLFVBQVVGO1lBQzFCO1lBRUEsZ0dBQWdHO1lBQ2hHbkMsVUFBVWtDLE1BQU0sQ0FBQztZQUNqQmxDLFVBQVVrQyxNQUFNLENBQUM7WUFDakJsQyxVQUFVa0MsTUFBTSxDQUFDO1lBRWpCaEMsT0FBT29CLE9BQU8sQ0FBQ2dCLFlBQVksQ0FDekIsQ0FBQyxHQUNELElBQ0EsR0FBOEJMLE9BQTNCL0IsT0FBT0MsUUFBUSxDQUFDb0MsUUFBUSxFQUFnQ3ZDLE9BQTdCaUMsa0JBQWtCLEtBQUssS0FBZ0IsT0FBVmpDO1FBRS9EO3dDQUFHO1FBQUNVO1FBQUtFO1FBQVNFO1FBQVVFO0tBQWdCO0lBRTVDLE1BQU13Qix1QkFBdUIsT0FBT0M7UUFNbEN2QixPQUFPdUIsT0FBTy9CLEdBQUc7UUFDakJTLFdBQVdzQixPQUFPN0IsT0FBTztRQUN6QlEsWUFBWXFCLE9BQU8zQixRQUFRO1FBQzNCTyxtQkFBbUJvQixPQUFPekIsZUFBZTtRQUV6QywyQkFBMkI7UUFDM0JTLHNCQUFzQmdCLE9BQU8vQixHQUFHLEVBQUUrQixPQUFPN0IsT0FBTztRQUVoRCxNQUFNOEIsV0FBeUI7WUFDN0JoQyxLQUFLK0IsT0FBTy9CLEdBQUc7WUFDZkUsU0FBUzZCLE9BQU83QixPQUFPO1lBQ3ZCRSxVQUFVMkIsT0FBTzNCLFFBQVE7WUFDekJFLGlCQUFpQnlCLE9BQU96QixlQUFlO1lBQ3ZDMkIsV0FBV0MsS0FBS0MsR0FBRztRQUNyQjtRQUVBdEIsV0FBVyxDQUFDdUI7WUFDVixNQUFNQyxpQkFBaUI7Z0JBQUNMO21CQUFhSTthQUFZLENBQUNFLEtBQUssQ0FBQyxHQUFHOUQ7WUFDM0QwQyxZQUFZbUI7WUFDWixPQUFPQTtRQUNUO0lBQ0Y7SUFFQSxNQUFNRSx5QkFBeUIsQ0FBQ0M7UUFDOUJoQyxPQUFPZ0MsTUFBTXhDLEdBQUc7UUFDaEJTLFdBQVcrQixNQUFNdEMsT0FBTztRQUN4QlEsWUFBWThCLE1BQU1wQyxRQUFRO1FBQzFCTyxtQkFBbUI2QixNQUFNbEMsZUFBZTtRQUV4QywyQkFBMkI7UUFDM0JVLGdCQUFnQixzQkFBc0J3QixNQUFNdEMsT0FBTztJQUNyRDtJQUVBLE1BQU11QywyQkFBMkIsQ0FBQ0M7UUFDaEMsTUFBTUwsaUJBQWlCekIsUUFBUStCLE1BQU0sQ0FBQyxDQUFDQyxHQUFHQyxJQUFNQSxNQUFNSDtRQUN0RDdCLFdBQVd3QjtRQUNYbkIsWUFBWW1CO1FBRVosK0JBQStCO1FBQy9CckIsZ0JBQWdCO0lBQ2xCO0lBRUEscUJBQ0UsOERBQUNwRCwrR0FBS0E7UUFBQ2tGLEtBQUk7OzBCQUNULDhEQUFDNUUsK0dBQUtBO2dCQUFDNkUsT0FBTzswQkFBRzs7Ozs7OzBCQUVqQiw4REFBQ2pGLDhHQUFJQTtnQkFBQ2tGLFFBQU87O2tDQUNYLDhEQUFDbEYsOEdBQUlBLENBQUNtRixHQUFHO3dCQUNQQyxNQUFNOzRCQUNKQyxJQUFJOzRCQUNKQyxJQUFJOzRCQUNKQyxJQUFJO3dCQUNOO3dCQUNBTixPQUFPOzRCQUFFTyxNQUFNOzRCQUFHRixJQUFJO3dCQUFFO2tDQUV4Qiw0RUFBQ25GLDhHQUFJQTs0QkFBQ3NGLFFBQU87NEJBQUtDLFNBQVE7NEJBQUtDLFFBQU87NEJBQUtDLFVBQVU7c0NBQ25ELDRFQUFDOUYsK0dBQUtBOztrREFDSiw4REFBQ08sOERBQVVBO3dDQUNUOEIsWUFBWUQ7d0NBQ1pHLGdCQUFnQkQ7d0NBQ2hCRyxpQkFBaUJEO3dDQUNqQkcsd0JBQXdCRDt3Q0FDeEJxRCxZQUFZN0I7d0NBQ1o4QixpQkFBaUJuRDt3Q0FDakJvRCxrQkFBa0JuRDt3Q0FDbEJvRCx5QkFBeUJuRDs7Ozs7O2tEQUUzQiw4REFBQzlDLCtHQUFJQTt3Q0FBQ2lGLEtBQUk7OzBEQUNSLDhEQUFDL0UsaUhBQU1BO2dEQUNMZ0csT0FBTztvREFBRUMsVUFBVTtnREFBRTtnREFDckJDLE1BQUs7Z0RBQ0xDLFNBQVE7Z0RBQ1JDLFNBQVM7b0RBQ1AzRSxPQUFPNEUsSUFBSSxDQUFDcEUsS0FBSztnREFDbkI7MERBQ0Q7Ozs7OzswREFHRCw4REFBQ2pDLGlIQUFNQTtnREFDTGdHLE9BQU87b0RBQUVDLFVBQVU7Z0RBQUU7Z0RBQ3JCQyxNQUFLO2dEQUNMQyxTQUFRO2dEQUNSQyxTQUFTO29EQUNQLElBQUlyRCxVQUFVdUQsT0FBTyxFQUFFO3dEQUNyQnZELFVBQVV1RCxPQUFPLENBQUNDLFFBQVEsQ0FBQzs0REFBRUMsTUFBTTs0REFBVUMsV0FBVzt3REFBTTt3REFDOUR2RCxvQkFBb0I7b0RBQ3RCO2dEQUNGOzBEQUNEOzs7Ozs7MERBR0QsOERBQUNsRCxpSEFBTUE7Z0RBQ0xnRyxPQUFPO29EQUFFQyxVQUFVO2dEQUFFO2dEQUNyQkMsTUFBSztnREFDTFEsT0FBTTtnREFDTlAsU0FBUTtnREFDUkMsU0FBUztvREFDUCx5QkFBeUI7b0RBQ3pCM0QsT0FBTy9CO29EQUNQZ0MsV0FBVy9CO29EQUNYZ0MsWUFBWS9CO29EQUNaZ0MsbUJBQW1CL0I7Z0RBQ3JCOzBEQUNEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU9ULDhEQUFDZCw4R0FBSUEsQ0FBQ21GLEdBQUc7d0JBQ1BDLE1BQU07NEJBQ0pDLElBQUk7NEJBQ0pDLElBQUk7NEJBQ0pDLElBQUk7d0JBQ047d0JBQ0FOLE9BQU87NEJBQUVPLE1BQU07NEJBQUdGLElBQUk7d0JBQUU7a0NBRXhCLDRFQUFDcEYsaUhBQU1BOzRCQUFDMEcsR0FBRTtzQ0FDUiw0RUFBQ3RHLG9FQUFhQTtnQ0FDWnVHLE1BQU0zRSxPQUFPO2dDQUNiRSxTQUFTQTtnQ0FDVEUsVUFBVUE7Z0NBQ1ZFLGlCQUFpQkE7Z0NBQ2pCUSxXQUFXQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNbkIsOERBQUN6QyxvRUFBYUE7Z0JBQ1p1QyxTQUFTQTtnQkFDVGdFLG9CQUFvQnJDO2dCQUNwQnNDLHNCQUFzQnBDOzs7Ozs7Ozs7Ozs7QUFJOUI7R0F4TXdCMUM7O1FBY2tEekIsNkRBQVlBOzs7S0FkOUR5QiIsInNvdXJjZXMiOlsiL1VzZXJzL3lhcGkvY29kZS9xci1jb2RlLWRvbmtleS9zcmMvUVJDb2RlR2VuZXJhdG9yUGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgU3RhY2ssIEZsZXgsIEdyaWQsIEJ1dHRvbiwgQ2VudGVyLCBDYXJkLCBUaXRsZSB9IGZyb20gJ0BtYW50aW5lL2NvcmUnO1xuaW1wb3J0IHR5cGUgeyBEb3RUeXBlLCBGaWxlRXh0ZW5zaW9uIH0gZnJvbSAncXItY29kZS1zdHlsaW5nJztcbmltcG9ydCBRUkNvZGVTdHlsaW5nIGZyb20gJ3FyLWNvZGUtc3R5bGluZyc7XG5pbXBvcnQgeyBRUkNvZGVGb3JtIH0gZnJvbSAnLi9jb21wb25lbnRzL1FSQ29kZUZvcm0nO1xuaW1wb3J0IHsgUVJDb2RlRGlzcGxheSB9IGZyb20gJy4vY29tcG9uZW50cy9RUkNvZGVEaXNwbGF5JztcbmltcG9ydCB7IFFSQ29kZUhpc3RvcnkgfSBmcm9tICcuL2NvbXBvbmVudHMvUVJDb2RlSGlzdG9yeSc7XG5pbXBvcnQgeyB1c2VBbmFseXRpY3MgfSBmcm9tICcuL2hvb2tzL3VzZUFuYWx5dGljcyc7XG5pbXBvcnQgeyB0cmFja0V2ZW50IH0gZnJvbSAnLi9jb21wb25lbnRzL0dvb2dsZUFuYWx5dGljcyc7XG5cbmludGVyZmFjZSBIaXN0b3J5RW50cnkge1xuICB1cmw6IHN0cmluZztcbiAgZG90VHlwZTogRG90VHlwZTtcbiAgZG90Q29sb3I6IHN0cmluZztcbiAgYmFja2dyb3VuZENvbG9yOiBzdHJpbmc7XG4gIHRpbWVzdGFtcDogbnVtYmVyO1xufVxuXG5jb25zdCBMT0NBTF9TVE9SQUdFX0tFWSA9ICdxcl9jb2RlX2hpc3RvcnknO1xuY29uc3QgTUFYX0hJU1RPUllfRU5UUklFUyA9IDEwMDtcblxuY29uc3QgREVGQVVMVF9VUkwgPSAnJztcbmNvbnN0IERFRkFVTFRfRE9UX1RZUEU6IERvdFR5cGUgPSAnc3F1YXJlJztcbmNvbnN0IERFRkFVTFRfRE9UX0NPTE9SID0gJyMwMDAwMDAnO1xuY29uc3QgREVGQVVMVF9CQUNLR1JPVU5EX0NPTE9SID0gJyNmZmZmZmYnO1xuXG5jb25zdCBsb2FkSGlzdG9yeSA9ICgpID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzdG9yZWRIaXN0b3J5ID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oTE9DQUxfU1RPUkFHRV9LRVkpO1xuICAgIHJldHVybiBzdG9yZWRIaXN0b3J5ID8gSlNPTi5wYXJzZShzdG9yZWRIaXN0b3J5KSA6IFtdO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGhpc3RvcnkgZnJvbSBsb2NhbCBzdG9yYWdlOicsIGVycm9yKTtcbiAgICByZXR1cm4gW107XG4gIH1cbn07XG5cbmNvbnN0IGdldEluaXRpYWxQYXJhbXMgPSAoKSA9PiB7XG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuIHt9O1xuICBjb25zdCB1cmxQYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKHdpbmRvdy5sb2NhdGlvbi5zZWFyY2gpO1xuICBjb25zdCBjb25maWdQYXJhbSA9IHVybFBhcmFtcy5nZXQoJ2NvbmZpZycpO1xuICBpZiAoY29uZmlnUGFyYW0pIHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgZGVjb2RlZENvbmZpZyA9IEpTT04ucGFyc2UoYXRvYihjb25maWdQYXJhbSkpO1xuICAgICAgcmV0dXJuIGRlY29kZWRDb25maWc7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBwYXJzZSBjb25maWcgZnJvbSBVUkw6JywgZXJyb3IpO1xuICAgIH1cbiAgfVxuICByZXR1cm4ge307XG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBRUkNvZGVHZW5lcmF0b3JQYWdlKCkge1xuICBjb25zdCB7XG4gICAgdXJsOiBpbml0aWFsVXJsID0gREVGQVVMVF9VUkwsXG4gICAgZG90VHlwZTogaW5pdGlhbERvdFR5cGUgPSBERUZBVUxUX0RPVF9UWVBFLFxuICAgIGRvdENvbG9yOiBpbml0aWFsRG90Q29sb3IgPSBERUZBVUxUX0RPVF9DT0xPUixcbiAgICBiYWNrZ3JvdW5kQ29sb3I6IGluaXRpYWxCYWNrZ3JvdW5kQ29sb3IgPSBERUZBVUxUX0JBQ0tHUk9VTkRfQ09MT1IsXG4gIH0gPSBnZXRJbml0aWFsUGFyYW1zKCk7XG4gIGNvbnN0IFt1cmwsIHNldFVybF0gPSB1c2VTdGF0ZShpbml0aWFsVXJsKTtcbiAgY29uc3QgW2RvdFR5cGUsIHNldERvdFR5cGVdID0gdXNlU3RhdGU8RG90VHlwZT4oaW5pdGlhbERvdFR5cGUpO1xuICBjb25zdCBbZG90Q29sb3IsIHNldERvdENvbG9yXSA9IHVzZVN0YXRlKGluaXRpYWxEb3RDb2xvcik7XG4gIGNvbnN0IFtiYWNrZ3JvdW5kQ29sb3IsIHNldEJhY2tncm91bmRDb2xvcl0gPSB1c2VTdGF0ZShpbml0aWFsQmFja2dyb3VuZENvbG9yKTtcbiAgY29uc3QgW2hpc3RvcnksIHNldEhpc3RvcnldID0gdXNlU3RhdGU8SGlzdG9yeUVudHJ5W10+KFtdKTtcbiAgY29uc3QgcXJDb2RlUmVmID0gdXNlUmVmPFFSQ29kZVN0eWxpbmcgfCBudWxsPihudWxsKTtcblxuICBjb25zdCB7IHRyYWNrUVJDb2RlR2VuZXJhdGlvbiwgdHJhY2tFbmdhZ2VtZW50LCB0cmFja1FSQ29kZURvd25sb2FkIH0gPSB1c2VBbmFseXRpY3MoKTtcblxuICBjb25zdCBzYXZlSGlzdG9yeSA9IChuZXdIaXN0b3J5OiBIaXN0b3J5RW50cnlbXSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShMT0NBTF9TVE9SQUdFX0tFWSwgSlNPTi5zdHJpbmdpZnkobmV3SGlzdG9yeSkpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc2F2ZSBoaXN0b3J5IHRvIGxvY2FsIHN0b3JhZ2U6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNldEhpc3RvcnkobG9hZEhpc3RvcnkoKSk7XG4gIH0sIFtdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHVybFBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMod2luZG93LmxvY2F0aW9uLnNlYXJjaCk7XG4gICAgY29uc3QgY29uZmlnID0geyB1cmwsIGRvdFR5cGUsIGRvdENvbG9yLCBiYWNrZ3JvdW5kQ29sb3IgfTtcblxuICAgIGNvbnN0IGlzRGVmYXVsdENvbmZpZyA9XG4gICAgICB1cmwgPT09IERFRkFVTFRfVVJMICYmXG4gICAgICBkb3RUeXBlID09PSBERUZBVUxUX0RPVF9UWVBFICYmXG4gICAgICBkb3RDb2xvciA9PT0gREVGQVVMVF9ET1RfQ09MT1IgJiZcbiAgICAgIGJhY2tncm91bmRDb2xvciA9PT0gREVGQVVMVF9CQUNLR1JPVU5EX0NPTE9SO1xuXG4gICAgaWYgKGlzRGVmYXVsdENvbmZpZykge1xuICAgICAgdXJsUGFyYW1zLmRlbGV0ZSgnY29uZmlnJyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnN0IGVuY29kZWRDb25maWcgPSBidG9hKEpTT04uc3RyaW5naWZ5KGNvbmZpZykpO1xuICAgICAgdXJsUGFyYW1zLnNldCgnY29uZmlnJywgZW5jb2RlZENvbmZpZyk7XG4gICAgfVxuXG4gICAgLy8gVGhlc2UgcGFyYW1ldGVycyBhcmUgbm93IGhhbmRsZWQgYnkgdGhlICdjb25maWcnIHBhcmFtZXRlciwgc28gdGhleSBzaG91bGQgYWx3YXlzIGJlIGRlbGV0ZWQuXG4gICAgdXJsUGFyYW1zLmRlbGV0ZSgnZG90VHlwZScpO1xuICAgIHVybFBhcmFtcy5kZWxldGUoJ2RvdENvbG9yJyk7XG4gICAgdXJsUGFyYW1zLmRlbGV0ZSgnYmFja2dyb3VuZENvbG9yJyk7XG5cbiAgICB3aW5kb3cuaGlzdG9yeS5yZXBsYWNlU3RhdGUoXG4gICAgICB7fSxcbiAgICAgICcnLFxuICAgICAgYCR7d2luZG93LmxvY2F0aW9uLnBhdGhuYW1lfSR7aXNEZWZhdWx0Q29uZmlnID8gJycgOiAnPyd9JHt1cmxQYXJhbXN9YFxuICAgICk7XG4gIH0sIFt1cmwsIGRvdFR5cGUsIGRvdENvbG9yLCBiYWNrZ3JvdW5kQ29sb3JdKTtcblxuICBjb25zdCBoYW5kbGVHZW5lcmF0ZVFSQ29kZSA9IGFzeW5jICh2YWx1ZXM6IHtcbiAgICB1cmw6IHN0cmluZztcbiAgICBkb3RUeXBlOiBEb3RUeXBlO1xuICAgIGRvdENvbG9yOiBzdHJpbmc7XG4gICAgYmFja2dyb3VuZENvbG9yOiBzdHJpbmc7XG4gIH0pID0+IHtcbiAgICBzZXRVcmwodmFsdWVzLnVybCk7XG4gICAgc2V0RG90VHlwZSh2YWx1ZXMuZG90VHlwZSk7XG4gICAgc2V0RG90Q29sb3IodmFsdWVzLmRvdENvbG9yKTtcbiAgICBzZXRCYWNrZ3JvdW5kQ29sb3IodmFsdWVzLmJhY2tncm91bmRDb2xvcik7XG5cbiAgICAvLyBUcmFjayBRUiBjb2RlIGdlbmVyYXRpb25cbiAgICB0cmFja1FSQ29kZUdlbmVyYXRpb24odmFsdWVzLnVybCwgdmFsdWVzLmRvdFR5cGUpO1xuXG4gICAgY29uc3QgbmV3RW50cnk6IEhpc3RvcnlFbnRyeSA9IHtcbiAgICAgIHVybDogdmFsdWVzLnVybCxcbiAgICAgIGRvdFR5cGU6IHZhbHVlcy5kb3RUeXBlLFxuICAgICAgZG90Q29sb3I6IHZhbHVlcy5kb3RDb2xvcixcbiAgICAgIGJhY2tncm91bmRDb2xvcjogdmFsdWVzLmJhY2tncm91bmRDb2xvcixcbiAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICB9O1xuXG4gICAgc2V0SGlzdG9yeSgocHJldkhpc3RvcnkpID0+IHtcbiAgICAgIGNvbnN0IHVwZGF0ZWRIaXN0b3J5ID0gW25ld0VudHJ5LCAuLi5wcmV2SGlzdG9yeV0uc2xpY2UoMCwgTUFYX0hJU1RPUllfRU5UUklFUyk7XG4gICAgICBzYXZlSGlzdG9yeSh1cGRhdGVkSGlzdG9yeSk7XG4gICAgICByZXR1cm4gdXBkYXRlZEhpc3Rvcnk7XG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlTG9hZEhpc3RvcnlFbnRyeSA9IChlbnRyeTogSGlzdG9yeUVudHJ5KSA9PiB7XG4gICAgc2V0VXJsKGVudHJ5LnVybCk7XG4gICAgc2V0RG90VHlwZShlbnRyeS5kb3RUeXBlKTtcbiAgICBzZXREb3RDb2xvcihlbnRyeS5kb3RDb2xvcik7XG4gICAgc2V0QmFja2dyb3VuZENvbG9yKGVudHJ5LmJhY2tncm91bmRDb2xvcik7XG5cbiAgICAvLyBUcmFjayBoaXN0b3J5IGVudHJ5IGxvYWRcbiAgICB0cmFja0VuZ2FnZW1lbnQoJ2xvYWRfaGlzdG9yeV9lbnRyeScsIGVudHJ5LmRvdFR5cGUpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZURlbGV0ZUhpc3RvcnlFbnRyeSA9IChpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgY29uc3QgdXBkYXRlZEhpc3RvcnkgPSBoaXN0b3J5LmZpbHRlcigoXywgaSkgPT4gaSAhPT0gaW5kZXgpO1xuICAgIHNldEhpc3RvcnkodXBkYXRlZEhpc3RvcnkpO1xuICAgIHNhdmVIaXN0b3J5KHVwZGF0ZWRIaXN0b3J5KTtcblxuICAgIC8vIFRyYWNrIGhpc3RvcnkgZW50cnkgZGVsZXRpb25cbiAgICB0cmFja0VuZ2FnZW1lbnQoJ2RlbGV0ZV9oaXN0b3J5X2VudHJ5Jyk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8U3RhY2sgZ2FwPVwieGxcIj5cbiAgICAgIDxUaXRsZSBvcmRlcj17MX0+UVIgQ29kZSBHZW5lcmF0b3I8L1RpdGxlPlxuXG4gICAgICA8R3JpZCBndXR0ZXI9XCJ4bFwiPlxuICAgICAgICA8R3JpZC5Db2xcbiAgICAgICAgICBzcGFuPXt7XG4gICAgICAgICAgICBzbTogMTIsXG4gICAgICAgICAgICBtZDogNixcbiAgICAgICAgICAgIGxnOiA4LFxuICAgICAgICAgIH19XG4gICAgICAgICAgb3JkZXI9e3sgYmFzZTogMiwgbWQ6IDEgfX1cbiAgICAgICAgPlxuICAgICAgICAgIDxDYXJkIHNoYWRvdz1cInNtXCIgcGFkZGluZz1cImxnXCIgcmFkaXVzPVwibWRcIiB3aXRoQm9yZGVyPlxuICAgICAgICAgICAgPFN0YWNrPlxuICAgICAgICAgICAgICA8UVJDb2RlRm9ybVxuICAgICAgICAgICAgICAgIGluaXRpYWxVcmw9e3VybH1cbiAgICAgICAgICAgICAgICBpbml0aWFsRG90VHlwZT17ZG90VHlwZX1cbiAgICAgICAgICAgICAgICBpbml0aWFsRG90Q29sb3I9e2RvdENvbG9yfVxuICAgICAgICAgICAgICAgIGluaXRpYWxCYWNrZ3JvdW5kQ29sb3I9e2JhY2tncm91bmRDb2xvcn1cbiAgICAgICAgICAgICAgICBvbkdlbmVyYXRlPXtoYW5kbGVHZW5lcmF0ZVFSQ29kZX1cbiAgICAgICAgICAgICAgICBvbkRvdFR5cGVDaGFuZ2U9e3NldERvdFR5cGV9XG4gICAgICAgICAgICAgICAgb25Eb3RDb2xvckNoYW5nZT17c2V0RG90Q29sb3J9XG4gICAgICAgICAgICAgICAgb25CYWNrZ3JvdW5kQ29sb3JDaGFuZ2U9e3NldEJhY2tncm91bmRDb2xvcn1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPEZsZXggZ2FwPVwibWRcIj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICBzdHlsZT17eyBmbGV4R3JvdzogMSB9fVxuICAgICAgICAgICAgICAgICAgc2l6ZT1cIm1kXCJcbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJsaWdodFwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHdpbmRvdy5vcGVuKHVybCwgJ19ibGFuaycpO1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBPcGVuIFVSTFxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGZsZXhHcm93OiAxIH19XG4gICAgICAgICAgICAgICAgICBzaXplPVwibWRcIlxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImxpZ2h0XCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHFyQ29kZVJlZi5jdXJyZW50KSB7XG4gICAgICAgICAgICAgICAgICAgICAgcXJDb2RlUmVmLmN1cnJlbnQuZG93bmxvYWQoeyBuYW1lOiAncXJjb2RlJywgZXh0ZW5zaW9uOiAncG5nJyB9KTtcbiAgICAgICAgICAgICAgICAgICAgICB0cmFja1FSQ29kZURvd25sb2FkKCdwbmcnKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBEb3dubG9hZCBRUiBDb2RlXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgZmxleEdyb3c6IDEgfX1cbiAgICAgICAgICAgICAgICAgIHNpemU9XCJtZFwiXG4gICAgICAgICAgICAgICAgICBjb2xvcj1cInBpbmtcIlxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImxpZ2h0XCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgLy8gc2V0IG9wdGlvbnMgdG8gZGVmYXVsdFxuICAgICAgICAgICAgICAgICAgICBzZXRVcmwoREVGQVVMVF9VUkwpO1xuICAgICAgICAgICAgICAgICAgICBzZXREb3RUeXBlKERFRkFVTFRfRE9UX1RZUEUpO1xuICAgICAgICAgICAgICAgICAgICBzZXREb3RDb2xvcihERUZBVUxUX0RPVF9DT0xPUik7XG4gICAgICAgICAgICAgICAgICAgIHNldEJhY2tncm91bmRDb2xvcihERUZBVUxUX0JBQ0tHUk9VTkRfQ09MT1IpO1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBDbGVhclxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L0ZsZXg+XG4gICAgICAgICAgICA8L1N0YWNrPlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgPC9HcmlkLkNvbD5cbiAgICAgICAgPEdyaWQuQ29sXG4gICAgICAgICAgc3Bhbj17e1xuICAgICAgICAgICAgc206IDEyLFxuICAgICAgICAgICAgbWQ6IDYsXG4gICAgICAgICAgICBsZzogNCxcbiAgICAgICAgICB9fVxuICAgICAgICAgIG9yZGVyPXt7IGJhc2U6IDEsIG1kOiAyIH19XG4gICAgICAgID5cbiAgICAgICAgICA8Q2VudGVyIGg9XCIxMDAlXCI+XG4gICAgICAgICAgICA8UVJDb2RlRGlzcGxheVxuICAgICAgICAgICAgICBkYXRhPXt1cmwgfHwgJ2h0dHBzOi8vd3d3LnFyY29kZS1kb25rZXkuY29tJ31cbiAgICAgICAgICAgICAgZG90VHlwZT17ZG90VHlwZX1cbiAgICAgICAgICAgICAgZG90Q29sb3I9e2RvdENvbG9yfVxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I9e2JhY2tncm91bmRDb2xvcn1cbiAgICAgICAgICAgICAgcXJDb2RlUmVmPXtxckNvZGVSZWZ9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvQ2VudGVyPlxuICAgICAgICA8L0dyaWQuQ29sPlxuICAgICAgPC9HcmlkPlxuXG4gICAgICA8UVJDb2RlSGlzdG9yeVxuICAgICAgICBoaXN0b3J5PXtoaXN0b3J5fVxuICAgICAgICBvbkxvYWRIaXN0b3J5RW50cnk9e2hhbmRsZUxvYWRIaXN0b3J5RW50cnl9XG4gICAgICAgIG9uRGVsZXRlSGlzdG9yeUVudHJ5PXtoYW5kbGVEZWxldGVIaXN0b3J5RW50cnl9XG4gICAgICAvPlxuICAgIDwvU3RhY2s+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VSZWYiLCJTdGFjayIsIkZsZXgiLCJHcmlkIiwiQnV0dG9uIiwiQ2VudGVyIiwiQ2FyZCIsIlRpdGxlIiwiUVJDb2RlRm9ybSIsIlFSQ29kZURpc3BsYXkiLCJRUkNvZGVIaXN0b3J5IiwidXNlQW5hbHl0aWNzIiwiTE9DQUxfU1RPUkFHRV9LRVkiLCJNQVhfSElTVE9SWV9FTlRSSUVTIiwiREVGQVVMVF9VUkwiLCJERUZBVUxUX0RPVF9UWVBFIiwiREVGQVVMVF9ET1RfQ09MT1IiLCJERUZBVUxUX0JBQ0tHUk9VTkRfQ09MT1IiLCJsb2FkSGlzdG9yeSIsInN0b3JlZEhpc3RvcnkiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiSlNPTiIsInBhcnNlIiwiZXJyb3IiLCJjb25zb2xlIiwiZ2V0SW5pdGlhbFBhcmFtcyIsInVybFBhcmFtcyIsIlVSTFNlYXJjaFBhcmFtcyIsIndpbmRvdyIsImxvY2F0aW9uIiwic2VhcmNoIiwiY29uZmlnUGFyYW0iLCJnZXQiLCJkZWNvZGVkQ29uZmlnIiwiYXRvYiIsIlFSQ29kZUdlbmVyYXRvclBhZ2UiLCJ1cmwiLCJpbml0aWFsVXJsIiwiZG90VHlwZSIsImluaXRpYWxEb3RUeXBlIiwiZG90Q29sb3IiLCJpbml0aWFsRG90Q29sb3IiLCJiYWNrZ3JvdW5kQ29sb3IiLCJpbml0aWFsQmFja2dyb3VuZENvbG9yIiwic2V0VXJsIiwic2V0RG90VHlwZSIsInNldERvdENvbG9yIiwic2V0QmFja2dyb3VuZENvbG9yIiwiaGlzdG9yeSIsInNldEhpc3RvcnkiLCJxckNvZGVSZWYiLCJ0cmFja1FSQ29kZUdlbmVyYXRpb24iLCJ0cmFja0VuZ2FnZW1lbnQiLCJ0cmFja1FSQ29kZURvd25sb2FkIiwic2F2ZUhpc3RvcnkiLCJuZXdIaXN0b3J5Iiwic2V0SXRlbSIsInN0cmluZ2lmeSIsImNvbmZpZyIsImlzRGVmYXVsdENvbmZpZyIsImRlbGV0ZSIsImVuY29kZWRDb25maWciLCJidG9hIiwic2V0IiwicmVwbGFjZVN0YXRlIiwicGF0aG5hbWUiLCJoYW5kbGVHZW5lcmF0ZVFSQ29kZSIsInZhbHVlcyIsIm5ld0VudHJ5IiwidGltZXN0YW1wIiwiRGF0ZSIsIm5vdyIsInByZXZIaXN0b3J5IiwidXBkYXRlZEhpc3RvcnkiLCJzbGljZSIsImhhbmRsZUxvYWRIaXN0b3J5RW50cnkiLCJlbnRyeSIsImhhbmRsZURlbGV0ZUhpc3RvcnlFbnRyeSIsImluZGV4IiwiZmlsdGVyIiwiXyIsImkiLCJnYXAiLCJvcmRlciIsImd1dHRlciIsIkNvbCIsInNwYW4iLCJzbSIsIm1kIiwibGciLCJiYXNlIiwic2hhZG93IiwicGFkZGluZyIsInJhZGl1cyIsIndpdGhCb3JkZXIiLCJvbkdlbmVyYXRlIiwib25Eb3RUeXBlQ2hhbmdlIiwib25Eb3RDb2xvckNoYW5nZSIsIm9uQmFja2dyb3VuZENvbG9yQ2hhbmdlIiwic3R5bGUiLCJmbGV4R3JvdyIsInNpemUiLCJ2YXJpYW50Iiwib25DbGljayIsIm9wZW4iLCJjdXJyZW50IiwiZG93bmxvYWQiLCJuYW1lIiwiZXh0ZW5zaW9uIiwiY29sb3IiLCJoIiwiZGF0YSIsIm9uTG9hZEhpc3RvcnlFbnRyeSIsIm9uRGVsZXRlSGlzdG9yeUVudHJ5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/QRCodeGeneratorPage.tsx\n"));

/***/ })

});