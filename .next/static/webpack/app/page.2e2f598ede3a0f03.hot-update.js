"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/QRCodeGeneratorPage.tsx":
/*!*************************************!*\
  !*** ./src/QRCodeGeneratorPage.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QRCodeGeneratorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_mantine_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Card/Card.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Flex/Flex.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Center/Center.mjs\");\n/* harmony import */ var _components_QRCodeForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/QRCodeForm */ \"(app-pages-browser)/./src/components/QRCodeForm.tsx\");\n/* harmony import */ var _components_QRCodeDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/QRCodeDisplay */ \"(app-pages-browser)/./src/components/QRCodeDisplay.tsx\");\n/* harmony import */ var _components_QRCodeHistory__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/QRCodeHistory */ \"(app-pages-browser)/./src/components/QRCodeHistory.tsx\");\n/* harmony import */ var _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./hooks/useAnalytics */ \"(app-pages-browser)/./src/hooks/useAnalytics.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst LOCAL_STORAGE_KEY = 'qr_code_history';\nconst MAX_HISTORY_ENTRIES = 100;\nconst DEFAULT_URL = '';\nconst DEFAULT_DOT_TYPE = 'square';\nconst DEFAULT_DOT_COLOR = '#000000';\nconst DEFAULT_BACKGROUND_COLOR = '#ffffff';\nconst loadHistory = ()=>{\n    try {\n        const storedHistory = localStorage.getItem(LOCAL_STORAGE_KEY);\n        return storedHistory ? JSON.parse(storedHistory) : [];\n    } catch (error) {\n        console.error('Failed to load history from local storage:', error);\n        return [];\n    }\n};\nconst getInitialParams = ()=>{\n    if (false) {}\n    const urlParams = new URLSearchParams(window.location.search);\n    const configParam = urlParams.get('config');\n    if (configParam) {\n        try {\n            const decodedConfig = JSON.parse(atob(configParam));\n            return decodedConfig;\n        } catch (error) {\n            console.error('Failed to parse config from URL:', error);\n        }\n    }\n    return {};\n};\nfunction QRCodeGeneratorPage() {\n    _s();\n    const { url: initialUrl = DEFAULT_URL, dotType: initialDotType = DEFAULT_DOT_TYPE, dotColor: initialDotColor = DEFAULT_DOT_COLOR, backgroundColor: initialBackgroundColor = DEFAULT_BACKGROUND_COLOR } = getInitialParams();\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialUrl);\n    const [dotType, setDotType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialDotType);\n    const [dotColor, setDotColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialDotColor);\n    const [backgroundColor, setBackgroundColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialBackgroundColor);\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const qrCodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { trackQRCodeGeneration, trackEngagement, trackQRCodeDownload } = (0,_hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_5__.useAnalytics)();\n    const saveHistory = (newHistory)=>{\n        try {\n            localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(newHistory));\n        } catch (error) {\n            console.error('Failed to save history to local storage:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QRCodeGeneratorPage.useEffect\": ()=>{\n            setHistory(loadHistory());\n        }\n    }[\"QRCodeGeneratorPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QRCodeGeneratorPage.useEffect\": ()=>{\n            const urlParams = new URLSearchParams(window.location.search);\n            const config = {\n                url,\n                dotType,\n                dotColor,\n                backgroundColor\n            };\n            const isDefaultConfig = url === DEFAULT_URL && dotType === DEFAULT_DOT_TYPE && dotColor === DEFAULT_DOT_COLOR && backgroundColor === DEFAULT_BACKGROUND_COLOR;\n            if (isDefaultConfig) {\n                urlParams.delete('config');\n            } else {\n                const encodedConfig = btoa(JSON.stringify(config));\n                urlParams.set('config', encodedConfig);\n            }\n            // These parameters are now handled by the 'config' parameter, so they should always be deleted.\n            urlParams.delete('dotType');\n            urlParams.delete('dotColor');\n            urlParams.delete('backgroundColor');\n            window.history.replaceState({}, '', \"\".concat(window.location.pathname).concat(isDefaultConfig ? '' : '?').concat(urlParams));\n        }\n    }[\"QRCodeGeneratorPage.useEffect\"], [\n        url,\n        dotType,\n        dotColor,\n        backgroundColor\n    ]);\n    const handleGenerateQRCode = async (values)=>{\n        setUrl(values.url);\n        setDotType(values.dotType);\n        setDotColor(values.dotColor);\n        setBackgroundColor(values.backgroundColor);\n        // Track QR code generation\n        trackQRCodeGeneration(values.url, values.dotType);\n        const newEntry = {\n            url: values.url,\n            dotType: values.dotType,\n            dotColor: values.dotColor,\n            backgroundColor: values.backgroundColor,\n            timestamp: Date.now()\n        };\n        setHistory((prevHistory)=>{\n            const updatedHistory = [\n                newEntry,\n                ...prevHistory\n            ].slice(0, MAX_HISTORY_ENTRIES);\n            saveHistory(updatedHistory);\n            return updatedHistory;\n        });\n    };\n    const handleLoadHistoryEntry = (entry)=>{\n        setUrl(entry.url);\n        setDotType(entry.dotType);\n        setDotColor(entry.dotColor);\n        setBackgroundColor(entry.backgroundColor);\n        // Track history entry load\n        trackEngagement('load_history_entry', entry.dotType);\n    };\n    const handleDeleteHistoryEntry = (index)=>{\n        const updatedHistory = history.filter((_, i)=>i !== index);\n        setHistory(updatedHistory);\n        saveHistory(updatedHistory);\n        // Track history entry deletion\n        trackEngagement('delete_history_entry');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Stack, {\n        gap: \"xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Grid, {\n                gutter: \"xl\",\n                breakpoints: {\n                    xs: '320px',\n                    sm: '640px',\n                    md: '768px',\n                    lg: '1024px',\n                    xl: '1200px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Grid.Col, {\n                        span: {\n                            sm: 12,\n                            md: 6,\n                            lg: 8\n                        },\n                        order: {\n                            base: 2,\n                            md: 1\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            shadow: \"sm\",\n                            padding: \"lg\",\n                            radius: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Stack, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QRCodeForm__WEBPACK_IMPORTED_MODULE_2__.QRCodeForm, {\n                                        initialUrl: url,\n                                        initialDotType: dotType,\n                                        initialDotColor: dotColor,\n                                        initialBackgroundColor: backgroundColor,\n                                        onGenerate: handleGenerateQRCode,\n                                        onDotTypeChange: setDotType,\n                                        onDotColorChange: setDotColor,\n                                        onBackgroundColorChange: setBackgroundColor\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Flex, {\n                                        gap: \"md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                                style: {\n                                                    flexGrow: 1\n                                                },\n                                                size: \"md\",\n                                                variant: \"light\",\n                                                onClick: ()=>{\n                                                    window.open(url, '_blank');\n                                                },\n                                                children: \"Open URL\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                                style: {\n                                                    flexGrow: 1\n                                                },\n                                                size: \"md\",\n                                                variant: \"light\",\n                                                onClick: ()=>{\n                                                    if (qrCodeRef.current) {\n                                                        qrCodeRef.current.download({\n                                                            name: 'qrcode',\n                                                            extension: 'png'\n                                                        });\n                                                        trackQRCodeDownload('png');\n                                                    }\n                                                },\n                                                children: \"Download QR Code\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                                style: {\n                                                    flexGrow: 1\n                                                },\n                                                size: \"md\",\n                                                color: \"pink\",\n                                                variant: \"light\",\n                                                onClick: ()=>{\n                                                    // set options to default\n                                                    setUrl(DEFAULT_URL);\n                                                    setDotType(DEFAULT_DOT_TYPE);\n                                                    setDotColor(DEFAULT_DOT_COLOR);\n                                                    setBackgroundColor(DEFAULT_BACKGROUND_COLOR);\n                                                },\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Grid.Col, {\n                        span: {\n                            sm: 12,\n                            md: 6,\n                            lg: 4\n                        },\n                        order: {\n                            base: 1,\n                            md: 2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Center, {\n                            h: \"100%\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QRCodeDisplay__WEBPACK_IMPORTED_MODULE_3__.QRCodeDisplay, {\n                                data: url || 'https://www.qrcode-donkey.com',\n                                dotType: dotType,\n                                dotColor: dotColor,\n                                backgroundColor: backgroundColor,\n                                qrCodeRef: qrCodeRef\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QRCodeHistory__WEBPACK_IMPORTED_MODULE_4__.QRCodeHistory, {\n                history: history,\n                onLoadHistoryEntry: handleLoadHistoryEntry,\n                onDeleteHistoryEntry: handleDeleteHistoryEntry\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, this);\n}\n_s(QRCodeGeneratorPage, \"GZoS88CFq3mT6V1JLoHR1P2AGLc=\", false, function() {\n    return [\n        _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_5__.useAnalytics\n    ];\n});\n_c = QRCodeGeneratorPage;\nvar _c;\n$RefreshReg$(_c, \"QRCodeGeneratorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/QRCodeGeneratorPage.tsx\n"));

/***/ })

});