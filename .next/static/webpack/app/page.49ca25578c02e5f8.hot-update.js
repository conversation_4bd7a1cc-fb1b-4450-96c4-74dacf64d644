"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/QRCodeGeneratorPage.tsx":
/*!*************************************!*\
  !*** ./src/QRCodeGeneratorPage.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QRCodeGeneratorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Stack/Stack.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Title/Title.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Grid/Grid.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Card/Card.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Flex/Flex.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Button/Button.mjs\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Center,Flex,Grid,Stack,Title!=!@mantine/core */ \"(app-pages-browser)/./node_modules/@mantine/core/esm/components/Center/Center.mjs\");\n/* harmony import */ var _components_QRCodeForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/QRCodeForm */ \"(app-pages-browser)/./src/components/QRCodeForm.tsx\");\n/* harmony import */ var _components_QRCodeDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/QRCodeDisplay */ \"(app-pages-browser)/./src/components/QRCodeDisplay.tsx\");\n/* harmony import */ var _components_QRCodeHistory__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/QRCodeHistory */ \"(app-pages-browser)/./src/components/QRCodeHistory.tsx\");\n/* harmony import */ var _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./hooks/useAnalytics */ \"(app-pages-browser)/./src/hooks/useAnalytics.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst LOCAL_STORAGE_KEY = 'qr_code_history';\nconst MAX_HISTORY_ENTRIES = 100;\nconst DEFAULT_URL = '';\nconst DEFAULT_DOT_TYPE = 'square';\nconst DEFAULT_DOT_COLOR = '#000000';\nconst DEFAULT_BACKGROUND_COLOR = '#ffffff';\nconst loadHistory = ()=>{\n    try {\n        const storedHistory = localStorage.getItem(LOCAL_STORAGE_KEY);\n        return storedHistory ? JSON.parse(storedHistory) : [];\n    } catch (error) {\n        console.error('Failed to load history from local storage:', error);\n        return [];\n    }\n};\nconst getInitialParams = ()=>{\n    if (false) {}\n    const urlParams = new URLSearchParams(window.location.search);\n    const configParam = urlParams.get('config');\n    if (configParam) {\n        try {\n            const decodedConfig = JSON.parse(atob(configParam));\n            return decodedConfig;\n        } catch (error) {\n            console.error('Failed to parse config from URL:', error);\n        }\n    }\n    return {};\n};\nfunction QRCodeGeneratorPage() {\n    _s();\n    const { url: initialUrl = DEFAULT_URL, dotType: initialDotType = DEFAULT_DOT_TYPE, dotColor: initialDotColor = DEFAULT_DOT_COLOR, backgroundColor: initialBackgroundColor = DEFAULT_BACKGROUND_COLOR } = getInitialParams();\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialUrl);\n    const [dotType, setDotType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialDotType);\n    const [dotColor, setDotColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialDotColor);\n    const [backgroundColor, setBackgroundColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialBackgroundColor);\n    const [history, setHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const qrCodeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { trackQRCodeGeneration, trackEngagement, trackQRCodeDownload } = (0,_hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_5__.useAnalytics)();\n    const saveHistory = (newHistory)=>{\n        try {\n            localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(newHistory));\n        } catch (error) {\n            console.error('Failed to save history to local storage:', error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QRCodeGeneratorPage.useEffect\": ()=>{\n            setHistory(loadHistory());\n        }\n    }[\"QRCodeGeneratorPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QRCodeGeneratorPage.useEffect\": ()=>{\n            const urlParams = new URLSearchParams(window.location.search);\n            const config = {\n                url,\n                dotType,\n                dotColor,\n                backgroundColor\n            };\n            const isDefaultConfig = url === DEFAULT_URL && dotType === DEFAULT_DOT_TYPE && dotColor === DEFAULT_DOT_COLOR && backgroundColor === DEFAULT_BACKGROUND_COLOR;\n            if (isDefaultConfig) {\n                urlParams.delete('config');\n            } else {\n                const encodedConfig = btoa(JSON.stringify(config));\n                urlParams.set('config', encodedConfig);\n            }\n            // These parameters are now handled by the 'config' parameter, so they should always be deleted.\n            urlParams.delete('dotType');\n            urlParams.delete('dotColor');\n            urlParams.delete('backgroundColor');\n            window.history.replaceState({}, '', \"\".concat(window.location.pathname).concat(isDefaultConfig ? '' : '?').concat(urlParams));\n        }\n    }[\"QRCodeGeneratorPage.useEffect\"], [\n        url,\n        dotType,\n        dotColor,\n        backgroundColor\n    ]);\n    const handleGenerateQRCode = async (values)=>{\n        setUrl(values.url);\n        setDotType(values.dotType);\n        setDotColor(values.dotColor);\n        setBackgroundColor(values.backgroundColor);\n        // Track QR code generation\n        trackQRCodeGeneration(values.url, values.dotType);\n        const newEntry = {\n            url: values.url,\n            dotType: values.dotType,\n            dotColor: values.dotColor,\n            backgroundColor: values.backgroundColor,\n            timestamp: Date.now()\n        };\n        setHistory((prevHistory)=>{\n            const updatedHistory = [\n                newEntry,\n                ...prevHistory\n            ].slice(0, MAX_HISTORY_ENTRIES);\n            saveHistory(updatedHistory);\n            return updatedHistory;\n        });\n    };\n    const handleLoadHistoryEntry = (entry)=>{\n        setUrl(entry.url);\n        setDotType(entry.dotType);\n        setDotColor(entry.dotColor);\n        setBackgroundColor(entry.backgroundColor);\n        // Track history entry load\n        trackEngagement('load_history_entry', entry.dotType);\n    };\n    const handleDeleteHistoryEntry = (index)=>{\n        const updatedHistory = history.filter((_, i)=>i !== index);\n        setHistory(updatedHistory);\n        saveHistory(updatedHistory);\n        // Track history entry deletion\n        trackEngagement('delete_history_entry');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Stack, {\n        gap: \"xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Title, {\n                order: 1,\n                children: \"QR Code Generator\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Grid, {\n                gutter: \"xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Grid.Col, {\n                        span: {\n                            sm: 12,\n                            md: 6,\n                            lg: 8\n                        },\n                        order: {\n                            base: 2,\n                            md: 1\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            shadow: \"sm\",\n                            padding: \"lg\",\n                            radius: \"md\",\n                            withBorder: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Stack, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QRCodeForm__WEBPACK_IMPORTED_MODULE_2__.QRCodeForm, {\n                                        initialUrl: url,\n                                        initialDotType: dotType,\n                                        initialDotColor: dotColor,\n                                        initialBackgroundColor: backgroundColor,\n                                        onGenerate: handleGenerateQRCode,\n                                        onDotTypeChange: setDotType,\n                                        onDotColorChange: setDotColor,\n                                        onBackgroundColorChange: setBackgroundColor\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_10__.Flex, {\n                                        gap: \"md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                style: {\n                                                    flexGrow: 1\n                                                },\n                                                size: \"md\",\n                                                variant: \"light\",\n                                                onClick: ()=>{\n                                                    window.open(url, '_blank');\n                                                },\n                                                children: \"Open URL\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                style: {\n                                                    flexGrow: 1\n                                                },\n                                                size: \"md\",\n                                                variant: \"light\",\n                                                onClick: ()=>{\n                                                    if (qrCodeRef.current) {\n                                                        qrCodeRef.current.download({\n                                                            name: 'qrcode',\n                                                            extension: 'png'\n                                                        });\n                                                        trackQRCodeDownload('png');\n                                                    }\n                                                },\n                                                children: \"Download QR Code\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                style: {\n                                                    flexGrow: 1\n                                                },\n                                                size: \"md\",\n                                                color: \"pink\",\n                                                variant: \"light\",\n                                                onClick: ()=>{\n                                                    // set options to default\n                                                    setUrl(DEFAULT_URL);\n                                                    setDotType(DEFAULT_DOT_TYPE);\n                                                    setDotColor(DEFAULT_DOT_COLOR);\n                                                    setBackgroundColor(DEFAULT_BACKGROUND_COLOR);\n                                                },\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_8__.Grid.Col, {\n                        span: {\n                            sm: 12,\n                            md: 6,\n                            lg: 4\n                        },\n                        order: {\n                            base: 1,\n                            md: 2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_12__.Center, {\n                            h: \"100%\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QRCodeDisplay__WEBPACK_IMPORTED_MODULE_3__.QRCodeDisplay, {\n                                data: url || 'https://www.qrcode-donkey.com',\n                                dotType: dotType,\n                                dotColor: dotColor,\n                                backgroundColor: backgroundColor,\n                                qrCodeRef: qrCodeRef\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Center_Flex_Grid_Stack_Title_mantine_core__WEBPACK_IMPORTED_MODULE_6__.Stack, {\n                w: \"100%\",\n                gap: \"md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QRCodeHistory__WEBPACK_IMPORTED_MODULE_4__.QRCodeHistory, {\n                    history: history,\n                    onLoadHistoryEntry: handleLoadHistoryEntry,\n                    onDeleteHistoryEntry: handleDeleteHistoryEntry\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/code/qr-code-donkey/src/QRCodeGeneratorPage.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, this);\n}\n_s(QRCodeGeneratorPage, \"GZoS88CFq3mT6V1JLoHR1P2AGLc=\", false, function() {\n    return [\n        _hooks_useAnalytics__WEBPACK_IMPORTED_MODULE_5__.useAnalytics\n    ];\n});\n_c = QRCodeGeneratorPage;\nvar _c;\n$RefreshReg$(_c, \"QRCodeGeneratorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9RUkNvZGVHZW5lcmF0b3JQYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNvRDtBQUMyQjtBQUcxQjtBQUNNO0FBQ0E7QUFDUDtBQVdwRCxNQUFNYyxvQkFBb0I7QUFDMUIsTUFBTUMsc0JBQXNCO0FBRTVCLE1BQU1DLGNBQWM7QUFDcEIsTUFBTUMsbUJBQTRCO0FBQ2xDLE1BQU1DLG9CQUFvQjtBQUMxQixNQUFNQywyQkFBMkI7QUFFakMsTUFBTUMsY0FBYztJQUNsQixJQUFJO1FBQ0YsTUFBTUMsZ0JBQWdCQyxhQUFhQyxPQUFPLENBQUNUO1FBQzNDLE9BQU9PLGdCQUFnQkcsS0FBS0MsS0FBSyxDQUFDSixpQkFBaUIsRUFBRTtJQUN2RCxFQUFFLE9BQU9LLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDhDQUE4Q0E7UUFDNUQsT0FBTyxFQUFFO0lBQ1g7QUFDRjtBQUVBLE1BQU1FLG1CQUFtQjtJQUN2QixJQUFJLEtBQTZCLEVBQUUsRUFBVTtJQUM3QyxNQUFNQyxZQUFZLElBQUlDLGdCQUFnQkMsT0FBT0MsUUFBUSxDQUFDQyxNQUFNO0lBQzVELE1BQU1DLGNBQWNMLFVBQVVNLEdBQUcsQ0FBQztJQUNsQyxJQUFJRCxhQUFhO1FBQ2YsSUFBSTtZQUNGLE1BQU1FLGdCQUFnQlosS0FBS0MsS0FBSyxDQUFDWSxLQUFLSDtZQUN0QyxPQUFPRTtRQUNULEVBQUUsT0FBT1YsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsb0NBQW9DQTtRQUNwRDtJQUNGO0lBQ0EsT0FBTyxDQUFDO0FBQ1Y7QUFFZSxTQUFTWTs7SUFDdEIsTUFBTSxFQUNKQyxLQUFLQyxhQUFheEIsV0FBVyxFQUM3QnlCLFNBQVNDLGlCQUFpQnpCLGdCQUFnQixFQUMxQzBCLFVBQVVDLGtCQUFrQjFCLGlCQUFpQixFQUM3QzJCLGlCQUFpQkMseUJBQXlCM0Isd0JBQXdCLEVBQ25FLEdBQUdTO0lBQ0osTUFBTSxDQUFDVyxLQUFLUSxPQUFPLEdBQUc5QywrQ0FBUUEsQ0FBQ3VDO0lBQy9CLE1BQU0sQ0FBQ0MsU0FBU08sV0FBVyxHQUFHL0MsK0NBQVFBLENBQVV5QztJQUNoRCxNQUFNLENBQUNDLFVBQVVNLFlBQVksR0FBR2hELCtDQUFRQSxDQUFDMkM7SUFDekMsTUFBTSxDQUFDQyxpQkFBaUJLLG1CQUFtQixHQUFHakQsK0NBQVFBLENBQUM2QztJQUN2RCxNQUFNLENBQUNLLFNBQVNDLFdBQVcsR0FBR25ELCtDQUFRQSxDQUFpQixFQUFFO0lBQ3pELE1BQU1vRCxZQUFZbkQsNkNBQU1BLENBQXVCO0lBRS9DLE1BQU0sRUFBRW9ELHFCQUFxQixFQUFFQyxlQUFlLEVBQUVDLG1CQUFtQixFQUFFLEdBQUczQyxpRUFBWUE7SUFFcEYsTUFBTTRDLGNBQWMsQ0FBQ0M7UUFDbkIsSUFBSTtZQUNGcEMsYUFBYXFDLE9BQU8sQ0FBQzdDLG1CQUFtQlUsS0FBS29DLFNBQVMsQ0FBQ0Y7UUFDekQsRUFBRSxPQUFPaEMsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNENBQTRDQTtRQUM1RDtJQUNGO0lBRUExQixnREFBU0E7eUNBQUM7WUFDUm9ELFdBQVdoQztRQUNiO3dDQUFHLEVBQUU7SUFFTHBCLGdEQUFTQTt5Q0FBQztZQUNSLE1BQU02QixZQUFZLElBQUlDLGdCQUFnQkMsT0FBT0MsUUFBUSxDQUFDQyxNQUFNO1lBQzVELE1BQU00QixTQUFTO2dCQUFFdEI7Z0JBQUtFO2dCQUFTRTtnQkFBVUU7WUFBZ0I7WUFFekQsTUFBTWlCLGtCQUNKdkIsUUFBUXZCLGVBQ1J5QixZQUFZeEIsb0JBQ1owQixhQUFhekIscUJBQ2IyQixvQkFBb0IxQjtZQUV0QixJQUFJMkMsaUJBQWlCO2dCQUNuQmpDLFVBQVVrQyxNQUFNLENBQUM7WUFDbkIsT0FBTztnQkFDTCxNQUFNQyxnQkFBZ0JDLEtBQUt6QyxLQUFLb0MsU0FBUyxDQUFDQztnQkFDMUNoQyxVQUFVcUMsR0FBRyxDQUFDLFVBQVVGO1lBQzFCO1lBRUEsZ0dBQWdHO1lBQ2hHbkMsVUFBVWtDLE1BQU0sQ0FBQztZQUNqQmxDLFVBQVVrQyxNQUFNLENBQUM7WUFDakJsQyxVQUFVa0MsTUFBTSxDQUFDO1lBRWpCaEMsT0FBT29CLE9BQU8sQ0FBQ2dCLFlBQVksQ0FDekIsQ0FBQyxHQUNELElBQ0EsR0FBOEJMLE9BQTNCL0IsT0FBT0MsUUFBUSxDQUFDb0MsUUFBUSxFQUFnQ3ZDLE9BQTdCaUMsa0JBQWtCLEtBQUssS0FBZ0IsT0FBVmpDO1FBRS9EO3dDQUFHO1FBQUNVO1FBQUtFO1FBQVNFO1FBQVVFO0tBQWdCO0lBRTVDLE1BQU13Qix1QkFBdUIsT0FBT0M7UUFNbEN2QixPQUFPdUIsT0FBTy9CLEdBQUc7UUFDakJTLFdBQVdzQixPQUFPN0IsT0FBTztRQUN6QlEsWUFBWXFCLE9BQU8zQixRQUFRO1FBQzNCTyxtQkFBbUJvQixPQUFPekIsZUFBZTtRQUV6QywyQkFBMkI7UUFDM0JTLHNCQUFzQmdCLE9BQU8vQixHQUFHLEVBQUUrQixPQUFPN0IsT0FBTztRQUVoRCxNQUFNOEIsV0FBeUI7WUFDN0JoQyxLQUFLK0IsT0FBTy9CLEdBQUc7WUFDZkUsU0FBUzZCLE9BQU83QixPQUFPO1lBQ3ZCRSxVQUFVMkIsT0FBTzNCLFFBQVE7WUFDekJFLGlCQUFpQnlCLE9BQU96QixlQUFlO1lBQ3ZDMkIsV0FBV0MsS0FBS0MsR0FBRztRQUNyQjtRQUVBdEIsV0FBVyxDQUFDdUI7WUFDVixNQUFNQyxpQkFBaUI7Z0JBQUNMO21CQUFhSTthQUFZLENBQUNFLEtBQUssQ0FBQyxHQUFHOUQ7WUFDM0QwQyxZQUFZbUI7WUFDWixPQUFPQTtRQUNUO0lBQ0Y7SUFFQSxNQUFNRSx5QkFBeUIsQ0FBQ0M7UUFDOUJoQyxPQUFPZ0MsTUFBTXhDLEdBQUc7UUFDaEJTLFdBQVcrQixNQUFNdEMsT0FBTztRQUN4QlEsWUFBWThCLE1BQU1wQyxRQUFRO1FBQzFCTyxtQkFBbUI2QixNQUFNbEMsZUFBZTtRQUV4QywyQkFBMkI7UUFDM0JVLGdCQUFnQixzQkFBc0J3QixNQUFNdEMsT0FBTztJQUNyRDtJQUVBLE1BQU11QywyQkFBMkIsQ0FBQ0M7UUFDaEMsTUFBTUwsaUJBQWlCekIsUUFBUStCLE1BQU0sQ0FBQyxDQUFDQyxHQUFHQyxJQUFNQSxNQUFNSDtRQUN0RDdCLFdBQVd3QjtRQUNYbkIsWUFBWW1CO1FBRVosK0JBQStCO1FBQy9CckIsZ0JBQWdCO0lBQ2xCO0lBRUEscUJBQ0UsOERBQUNwRCwrR0FBS0E7UUFBQ2tGLEtBQUk7OzBCQUNULDhEQUFDNUUsK0dBQUtBO2dCQUFDNkUsT0FBTzswQkFBRzs7Ozs7OzBCQUVqQiw4REFBQ2pGLDhHQUFJQTtnQkFBQ2tGLFFBQU87O2tDQUNYLDhEQUFDbEYsOEdBQUlBLENBQUNtRixHQUFHO3dCQUNQQyxNQUFNOzRCQUNKQyxJQUFJOzRCQUNKQyxJQUFJOzRCQUNKQyxJQUFJO3dCQUNOO3dCQUNBTixPQUFPOzRCQUFFTyxNQUFNOzRCQUFHRixJQUFJO3dCQUFFO2tDQUV4Qiw0RUFBQ25GLDhHQUFJQTs0QkFBQ3NGLFFBQU87NEJBQUtDLFNBQVE7NEJBQUtDLFFBQU87NEJBQUtDLFVBQVU7c0NBQ25ELDRFQUFDOUYsK0dBQUtBOztrREFDSiw4REFBQ08sOERBQVVBO3dDQUNUOEIsWUFBWUQ7d0NBQ1pHLGdCQUFnQkQ7d0NBQ2hCRyxpQkFBaUJEO3dDQUNqQkcsd0JBQXdCRDt3Q0FDeEJxRCxZQUFZN0I7d0NBQ1o4QixpQkFBaUJuRDt3Q0FDakJvRCxrQkFBa0JuRDt3Q0FDbEJvRCx5QkFBeUJuRDs7Ozs7O2tEQUUzQiw4REFBQzlDLCtHQUFJQTt3Q0FBQ2lGLEtBQUk7OzBEQUNSLDhEQUFDL0UsaUhBQU1BO2dEQUNMZ0csT0FBTztvREFBRUMsVUFBVTtnREFBRTtnREFDckJDLE1BQUs7Z0RBQ0xDLFNBQVE7Z0RBQ1JDLFNBQVM7b0RBQ1AzRSxPQUFPNEUsSUFBSSxDQUFDcEUsS0FBSztnREFDbkI7MERBQ0Q7Ozs7OzswREFHRCw4REFBQ2pDLGlIQUFNQTtnREFDTGdHLE9BQU87b0RBQUVDLFVBQVU7Z0RBQUU7Z0RBQ3JCQyxNQUFLO2dEQUNMQyxTQUFRO2dEQUNSQyxTQUFTO29EQUNQLElBQUlyRCxVQUFVdUQsT0FBTyxFQUFFO3dEQUNyQnZELFVBQVV1RCxPQUFPLENBQUNDLFFBQVEsQ0FBQzs0REFBRUMsTUFBTTs0REFBVUMsV0FBVzt3REFBTTt3REFDOUR2RCxvQkFBb0I7b0RBQ3RCO2dEQUNGOzBEQUNEOzs7Ozs7MERBR0QsOERBQUNsRCxpSEFBTUE7Z0RBQ0xnRyxPQUFPO29EQUFFQyxVQUFVO2dEQUFFO2dEQUNyQkMsTUFBSztnREFDTFEsT0FBTTtnREFDTlAsU0FBUTtnREFDUkMsU0FBUztvREFDUCx5QkFBeUI7b0RBQ3pCM0QsT0FBTy9CO29EQUNQZ0MsV0FBVy9CO29EQUNYZ0MsWUFBWS9CO29EQUNaZ0MsbUJBQW1CL0I7Z0RBQ3JCOzBEQUNEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU9ULDhEQUFDZCw4R0FBSUEsQ0FBQ21GLEdBQUc7d0JBQ1BDLE1BQU07NEJBQ0pDLElBQUk7NEJBQ0pDLElBQUk7NEJBQ0pDLElBQUk7d0JBQ047d0JBQ0FOLE9BQU87NEJBQUVPLE1BQU07NEJBQUdGLElBQUk7d0JBQUU7a0NBRXhCLDRFQUFDcEYsaUhBQU1BOzRCQUFDMEcsR0FBRTtzQ0FDUiw0RUFBQ3RHLG9FQUFhQTtnQ0FDWnVHLE1BQU0zRSxPQUFPO2dDQUNiRSxTQUFTQTtnQ0FDVEUsVUFBVUE7Z0NBQ1ZFLGlCQUFpQkE7Z0NBQ2pCUSxXQUFXQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNbkIsOERBQUNsRCwrR0FBS0E7Z0JBQUNnSCxHQUFFO2dCQUFPOUIsS0FBSTswQkFDbEIsNEVBQUN6RSxvRUFBYUE7b0JBQ1p1QyxTQUFTQTtvQkFDVGlFLG9CQUFvQnRDO29CQUNwQnVDLHNCQUFzQnJDOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtoQztHQTFNd0IxQzs7UUFja0R6Qiw2REFBWUE7OztLQWQ5RHlCIiwic291cmNlcyI6WyIvVXNlcnMveWFwaS9jb2RlL3FyLWNvZGUtZG9ua2V5L3NyYy9RUkNvZGVHZW5lcmF0b3JQYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlLCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBTdGFjaywgRmxleCwgR3JpZCwgQnV0dG9uLCBDZW50ZXIsIENhcmQsIFRpdGxlIH0gZnJvbSAnQG1hbnRpbmUvY29yZSc7XG5pbXBvcnQgdHlwZSB7IERvdFR5cGUsIEZpbGVFeHRlbnNpb24gfSBmcm9tICdxci1jb2RlLXN0eWxpbmcnO1xuaW1wb3J0IFFSQ29kZVN0eWxpbmcgZnJvbSAncXItY29kZS1zdHlsaW5nJztcbmltcG9ydCB7IFFSQ29kZUZvcm0gfSBmcm9tICcuL2NvbXBvbmVudHMvUVJDb2RlRm9ybSc7XG5pbXBvcnQgeyBRUkNvZGVEaXNwbGF5IH0gZnJvbSAnLi9jb21wb25lbnRzL1FSQ29kZURpc3BsYXknO1xuaW1wb3J0IHsgUVJDb2RlSGlzdG9yeSB9IGZyb20gJy4vY29tcG9uZW50cy9RUkNvZGVIaXN0b3J5JztcbmltcG9ydCB7IHVzZUFuYWx5dGljcyB9IGZyb20gJy4vaG9va3MvdXNlQW5hbHl0aWNzJztcbmltcG9ydCB7IHRyYWNrRXZlbnQgfSBmcm9tICcuL2NvbXBvbmVudHMvR29vZ2xlQW5hbHl0aWNzJztcblxuaW50ZXJmYWNlIEhpc3RvcnlFbnRyeSB7XG4gIHVybDogc3RyaW5nO1xuICBkb3RUeXBlOiBEb3RUeXBlO1xuICBkb3RDb2xvcjogc3RyaW5nO1xuICBiYWNrZ3JvdW5kQ29sb3I6IHN0cmluZztcbiAgdGltZXN0YW1wOiBudW1iZXI7XG59XG5cbmNvbnN0IExPQ0FMX1NUT1JBR0VfS0VZID0gJ3FyX2NvZGVfaGlzdG9yeSc7XG5jb25zdCBNQVhfSElTVE9SWV9FTlRSSUVTID0gMTAwO1xuXG5jb25zdCBERUZBVUxUX1VSTCA9ICcnO1xuY29uc3QgREVGQVVMVF9ET1RfVFlQRTogRG90VHlwZSA9ICdzcXVhcmUnO1xuY29uc3QgREVGQVVMVF9ET1RfQ09MT1IgPSAnIzAwMDAwMCc7XG5jb25zdCBERUZBVUxUX0JBQ0tHUk9VTkRfQ09MT1IgPSAnI2ZmZmZmZic7XG5cbmNvbnN0IGxvYWRIaXN0b3J5ID0gKCkgPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IHN0b3JlZEhpc3RvcnkgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShMT0NBTF9TVE9SQUdFX0tFWSk7XG4gICAgcmV0dXJuIHN0b3JlZEhpc3RvcnkgPyBKU09OLnBhcnNlKHN0b3JlZEhpc3RvcnkpIDogW107XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgaGlzdG9yeSBmcm9tIGxvY2FsIHN0b3JhZ2U6JywgZXJyb3IpO1xuICAgIHJldHVybiBbXTtcbiAgfVxufTtcblxuY29uc3QgZ2V0SW5pdGlhbFBhcmFtcyA9ICgpID0+IHtcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4ge307XG4gIGNvbnN0IHVybFBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMod2luZG93LmxvY2F0aW9uLnNlYXJjaCk7XG4gIGNvbnN0IGNvbmZpZ1BhcmFtID0gdXJsUGFyYW1zLmdldCgnY29uZmlnJyk7XG4gIGlmIChjb25maWdQYXJhbSkge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBkZWNvZGVkQ29uZmlnID0gSlNPTi5wYXJzZShhdG9iKGNvbmZpZ1BhcmFtKSk7XG4gICAgICByZXR1cm4gZGVjb2RlZENvbmZpZztcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHBhcnNlIGNvbmZpZyBmcm9tIFVSTDonLCBlcnJvcik7XG4gICAgfVxuICB9XG4gIHJldHVybiB7fTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFFSQ29kZUdlbmVyYXRvclBhZ2UoKSB7XG4gIGNvbnN0IHtcbiAgICB1cmw6IGluaXRpYWxVcmwgPSBERUZBVUxUX1VSTCxcbiAgICBkb3RUeXBlOiBpbml0aWFsRG90VHlwZSA9IERFRkFVTFRfRE9UX1RZUEUsXG4gICAgZG90Q29sb3I6IGluaXRpYWxEb3RDb2xvciA9IERFRkFVTFRfRE9UX0NPTE9SLFxuICAgIGJhY2tncm91bmRDb2xvcjogaW5pdGlhbEJhY2tncm91bmRDb2xvciA9IERFRkFVTFRfQkFDS0dST1VORF9DT0xPUixcbiAgfSA9IGdldEluaXRpYWxQYXJhbXMoKTtcbiAgY29uc3QgW3VybCwgc2V0VXJsXSA9IHVzZVN0YXRlKGluaXRpYWxVcmwpO1xuICBjb25zdCBbZG90VHlwZSwgc2V0RG90VHlwZV0gPSB1c2VTdGF0ZTxEb3RUeXBlPihpbml0aWFsRG90VHlwZSk7XG4gIGNvbnN0IFtkb3RDb2xvciwgc2V0RG90Q29sb3JdID0gdXNlU3RhdGUoaW5pdGlhbERvdENvbG9yKTtcbiAgY29uc3QgW2JhY2tncm91bmRDb2xvciwgc2V0QmFja2dyb3VuZENvbG9yXSA9IHVzZVN0YXRlKGluaXRpYWxCYWNrZ3JvdW5kQ29sb3IpO1xuICBjb25zdCBbaGlzdG9yeSwgc2V0SGlzdG9yeV0gPSB1c2VTdGF0ZTxIaXN0b3J5RW50cnlbXT4oW10pO1xuICBjb25zdCBxckNvZGVSZWYgPSB1c2VSZWY8UVJDb2RlU3R5bGluZyB8IG51bGw+KG51bGwpO1xuXG4gIGNvbnN0IHsgdHJhY2tRUkNvZGVHZW5lcmF0aW9uLCB0cmFja0VuZ2FnZW1lbnQsIHRyYWNrUVJDb2RlRG93bmxvYWQgfSA9IHVzZUFuYWx5dGljcygpO1xuXG4gIGNvbnN0IHNhdmVIaXN0b3J5ID0gKG5ld0hpc3Rvcnk6IEhpc3RvcnlFbnRyeVtdKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKExPQ0FMX1NUT1JBR0VfS0VZLCBKU09OLnN0cmluZ2lmeShuZXdIaXN0b3J5KSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBzYXZlIGhpc3RvcnkgdG8gbG9jYWwgc3RvcmFnZTonLCBlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0SGlzdG9yeShsb2FkSGlzdG9yeSgpKTtcbiAgfSwgW10pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdXJsUGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyh3aW5kb3cubG9jYXRpb24uc2VhcmNoKTtcbiAgICBjb25zdCBjb25maWcgPSB7IHVybCwgZG90VHlwZSwgZG90Q29sb3IsIGJhY2tncm91bmRDb2xvciB9O1xuXG4gICAgY29uc3QgaXNEZWZhdWx0Q29uZmlnID1cbiAgICAgIHVybCA9PT0gREVGQVVMVF9VUkwgJiZcbiAgICAgIGRvdFR5cGUgPT09IERFRkFVTFRfRE9UX1RZUEUgJiZcbiAgICAgIGRvdENvbG9yID09PSBERUZBVUxUX0RPVF9DT0xPUiAmJlxuICAgICAgYmFja2dyb3VuZENvbG9yID09PSBERUZBVUxUX0JBQ0tHUk9VTkRfQ09MT1I7XG5cbiAgICBpZiAoaXNEZWZhdWx0Q29uZmlnKSB7XG4gICAgICB1cmxQYXJhbXMuZGVsZXRlKCdjb25maWcnKTtcbiAgICB9IGVsc2Uge1xuICAgICAgY29uc3QgZW5jb2RlZENvbmZpZyA9IGJ0b2EoSlNPTi5zdHJpbmdpZnkoY29uZmlnKSk7XG4gICAgICB1cmxQYXJhbXMuc2V0KCdjb25maWcnLCBlbmNvZGVkQ29uZmlnKTtcbiAgICB9XG5cbiAgICAvLyBUaGVzZSBwYXJhbWV0ZXJzIGFyZSBub3cgaGFuZGxlZCBieSB0aGUgJ2NvbmZpZycgcGFyYW1ldGVyLCBzbyB0aGV5IHNob3VsZCBhbHdheXMgYmUgZGVsZXRlZC5cbiAgICB1cmxQYXJhbXMuZGVsZXRlKCdkb3RUeXBlJyk7XG4gICAgdXJsUGFyYW1zLmRlbGV0ZSgnZG90Q29sb3InKTtcbiAgICB1cmxQYXJhbXMuZGVsZXRlKCdiYWNrZ3JvdW5kQ29sb3InKTtcblxuICAgIHdpbmRvdy5oaXN0b3J5LnJlcGxhY2VTdGF0ZShcbiAgICAgIHt9LFxuICAgICAgJycsXG4gICAgICBgJHt3aW5kb3cubG9jYXRpb24ucGF0aG5hbWV9JHtpc0RlZmF1bHRDb25maWcgPyAnJyA6ICc/J30ke3VybFBhcmFtc31gXG4gICAgKTtcbiAgfSwgW3VybCwgZG90VHlwZSwgZG90Q29sb3IsIGJhY2tncm91bmRDb2xvcl0pO1xuXG4gIGNvbnN0IGhhbmRsZUdlbmVyYXRlUVJDb2RlID0gYXN5bmMgKHZhbHVlczoge1xuICAgIHVybDogc3RyaW5nO1xuICAgIGRvdFR5cGU6IERvdFR5cGU7XG4gICAgZG90Q29sb3I6IHN0cmluZztcbiAgICBiYWNrZ3JvdW5kQ29sb3I6IHN0cmluZztcbiAgfSkgPT4ge1xuICAgIHNldFVybCh2YWx1ZXMudXJsKTtcbiAgICBzZXREb3RUeXBlKHZhbHVlcy5kb3RUeXBlKTtcbiAgICBzZXREb3RDb2xvcih2YWx1ZXMuZG90Q29sb3IpO1xuICAgIHNldEJhY2tncm91bmRDb2xvcih2YWx1ZXMuYmFja2dyb3VuZENvbG9yKTtcblxuICAgIC8vIFRyYWNrIFFSIGNvZGUgZ2VuZXJhdGlvblxuICAgIHRyYWNrUVJDb2RlR2VuZXJhdGlvbih2YWx1ZXMudXJsLCB2YWx1ZXMuZG90VHlwZSk7XG5cbiAgICBjb25zdCBuZXdFbnRyeTogSGlzdG9yeUVudHJ5ID0ge1xuICAgICAgdXJsOiB2YWx1ZXMudXJsLFxuICAgICAgZG90VHlwZTogdmFsdWVzLmRvdFR5cGUsXG4gICAgICBkb3RDb2xvcjogdmFsdWVzLmRvdENvbG9yLFxuICAgICAgYmFja2dyb3VuZENvbG9yOiB2YWx1ZXMuYmFja2dyb3VuZENvbG9yLFxuICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgIH07XG5cbiAgICBzZXRIaXN0b3J5KChwcmV2SGlzdG9yeSkgPT4ge1xuICAgICAgY29uc3QgdXBkYXRlZEhpc3RvcnkgPSBbbmV3RW50cnksIC4uLnByZXZIaXN0b3J5XS5zbGljZSgwLCBNQVhfSElTVE9SWV9FTlRSSUVTKTtcbiAgICAgIHNhdmVIaXN0b3J5KHVwZGF0ZWRIaXN0b3J5KTtcbiAgICAgIHJldHVybiB1cGRhdGVkSGlzdG9yeTtcbiAgICB9KTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVMb2FkSGlzdG9yeUVudHJ5ID0gKGVudHJ5OiBIaXN0b3J5RW50cnkpID0+IHtcbiAgICBzZXRVcmwoZW50cnkudXJsKTtcbiAgICBzZXREb3RUeXBlKGVudHJ5LmRvdFR5cGUpO1xuICAgIHNldERvdENvbG9yKGVudHJ5LmRvdENvbG9yKTtcbiAgICBzZXRCYWNrZ3JvdW5kQ29sb3IoZW50cnkuYmFja2dyb3VuZENvbG9yKTtcblxuICAgIC8vIFRyYWNrIGhpc3RvcnkgZW50cnkgbG9hZFxuICAgIHRyYWNrRW5nYWdlbWVudCgnbG9hZF9oaXN0b3J5X2VudHJ5JywgZW50cnkuZG90VHlwZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRGVsZXRlSGlzdG9yeUVudHJ5ID0gKGluZGV4OiBudW1iZXIpID0+IHtcbiAgICBjb25zdCB1cGRhdGVkSGlzdG9yeSA9IGhpc3RvcnkuZmlsdGVyKChfLCBpKSA9PiBpICE9PSBpbmRleCk7XG4gICAgc2V0SGlzdG9yeSh1cGRhdGVkSGlzdG9yeSk7XG4gICAgc2F2ZUhpc3RvcnkodXBkYXRlZEhpc3RvcnkpO1xuXG4gICAgLy8gVHJhY2sgaGlzdG9yeSBlbnRyeSBkZWxldGlvblxuICAgIHRyYWNrRW5nYWdlbWVudCgnZGVsZXRlX2hpc3RvcnlfZW50cnknKTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxTdGFjayBnYXA9XCJ4bFwiPlxuICAgICAgPFRpdGxlIG9yZGVyPXsxfT5RUiBDb2RlIEdlbmVyYXRvcjwvVGl0bGU+XG5cbiAgICAgIDxHcmlkIGd1dHRlcj1cInhsXCI+XG4gICAgICAgIDxHcmlkLkNvbFxuICAgICAgICAgIHNwYW49e3tcbiAgICAgICAgICAgIHNtOiAxMixcbiAgICAgICAgICAgIG1kOiA2LFxuICAgICAgICAgICAgbGc6IDgsXG4gICAgICAgICAgfX1cbiAgICAgICAgICBvcmRlcj17eyBiYXNlOiAyLCBtZDogMSB9fVxuICAgICAgICA+XG4gICAgICAgICAgPENhcmQgc2hhZG93PVwic21cIiBwYWRkaW5nPVwibGdcIiByYWRpdXM9XCJtZFwiIHdpdGhCb3JkZXI+XG4gICAgICAgICAgICA8U3RhY2s+XG4gICAgICAgICAgICAgIDxRUkNvZGVGb3JtXG4gICAgICAgICAgICAgICAgaW5pdGlhbFVybD17dXJsfVxuICAgICAgICAgICAgICAgIGluaXRpYWxEb3RUeXBlPXtkb3RUeXBlfVxuICAgICAgICAgICAgICAgIGluaXRpYWxEb3RDb2xvcj17ZG90Q29sb3J9XG4gICAgICAgICAgICAgICAgaW5pdGlhbEJhY2tncm91bmRDb2xvcj17YmFja2dyb3VuZENvbG9yfVxuICAgICAgICAgICAgICAgIG9uR2VuZXJhdGU9e2hhbmRsZUdlbmVyYXRlUVJDb2RlfVxuICAgICAgICAgICAgICAgIG9uRG90VHlwZUNoYW5nZT17c2V0RG90VHlwZX1cbiAgICAgICAgICAgICAgICBvbkRvdENvbG9yQ2hhbmdlPXtzZXREb3RDb2xvcn1cbiAgICAgICAgICAgICAgICBvbkJhY2tncm91bmRDb2xvckNoYW5nZT17c2V0QmFja2dyb3VuZENvbG9yfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8RmxleCBnYXA9XCJtZFwiPlxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGZsZXhHcm93OiAxIH19XG4gICAgICAgICAgICAgICAgICBzaXplPVwibWRcIlxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImxpZ2h0XCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgd2luZG93Lm9wZW4odXJsLCAnX2JsYW5rJyk7XG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIE9wZW4gVVJMXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgZmxleEdyb3c6IDEgfX1cbiAgICAgICAgICAgICAgICAgIHNpemU9XCJtZFwiXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwibGlnaHRcIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBpZiAocXJDb2RlUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgICAgICAgICAgICAgICBxckNvZGVSZWYuY3VycmVudC5kb3dubG9hZCh7IG5hbWU6ICdxcmNvZGUnLCBleHRlbnNpb246ICdwbmcnIH0pO1xuICAgICAgICAgICAgICAgICAgICAgIHRyYWNrUVJDb2RlRG93bmxvYWQoJ3BuZycpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIERvd25sb2FkIFFSIENvZGVcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICBzdHlsZT17eyBmbGV4R3JvdzogMSB9fVxuICAgICAgICAgICAgICAgICAgc2l6ZT1cIm1kXCJcbiAgICAgICAgICAgICAgICAgIGNvbG9yPVwicGlua1wiXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwibGlnaHRcIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAvLyBzZXQgb3B0aW9ucyB0byBkZWZhdWx0XG4gICAgICAgICAgICAgICAgICAgIHNldFVybChERUZBVUxUX1VSTCk7XG4gICAgICAgICAgICAgICAgICAgIHNldERvdFR5cGUoREVGQVVMVF9ET1RfVFlQRSk7XG4gICAgICAgICAgICAgICAgICAgIHNldERvdENvbG9yKERFRkFVTFRfRE9UX0NPTE9SKTtcbiAgICAgICAgICAgICAgICAgICAgc2V0QmFja2dyb3VuZENvbG9yKERFRkFVTFRfQkFDS0dST1VORF9DT0xPUik7XG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIENsZWFyXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvRmxleD5cbiAgICAgICAgICAgIDwvU3RhY2s+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICA8L0dyaWQuQ29sPlxuICAgICAgICA8R3JpZC5Db2xcbiAgICAgICAgICBzcGFuPXt7XG4gICAgICAgICAgICBzbTogMTIsXG4gICAgICAgICAgICBtZDogNixcbiAgICAgICAgICAgIGxnOiA0LFxuICAgICAgICAgIH19XG4gICAgICAgICAgb3JkZXI9e3sgYmFzZTogMSwgbWQ6IDIgfX1cbiAgICAgICAgPlxuICAgICAgICAgIDxDZW50ZXIgaD1cIjEwMCVcIj5cbiAgICAgICAgICAgIDxRUkNvZGVEaXNwbGF5XG4gICAgICAgICAgICAgIGRhdGE9e3VybCB8fCAnaHR0cHM6Ly93d3cucXJjb2RlLWRvbmtleS5jb20nfVxuICAgICAgICAgICAgICBkb3RUeXBlPXtkb3RUeXBlfVxuICAgICAgICAgICAgICBkb3RDb2xvcj17ZG90Q29sb3J9XG4gICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcj17YmFja2dyb3VuZENvbG9yfVxuICAgICAgICAgICAgICBxckNvZGVSZWY9e3FyQ29kZVJlZn1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9DZW50ZXI+XG4gICAgICAgIDwvR3JpZC5Db2w+XG4gICAgICA8L0dyaWQ+XG5cbiAgICAgIDxTdGFjayB3PVwiMTAwJVwiIGdhcD1cIm1kXCI+XG4gICAgICAgIDxRUkNvZGVIaXN0b3J5XG4gICAgICAgICAgaGlzdG9yeT17aGlzdG9yeX1cbiAgICAgICAgICBvbkxvYWRIaXN0b3J5RW50cnk9e2hhbmRsZUxvYWRIaXN0b3J5RW50cnl9XG4gICAgICAgICAgb25EZWxldGVIaXN0b3J5RW50cnk9e2hhbmRsZURlbGV0ZUhpc3RvcnlFbnRyeX1cbiAgICAgICAgLz5cbiAgICAgIDwvU3RhY2s+XG4gICAgPC9TdGFjaz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZVJlZiIsIlN0YWNrIiwiRmxleCIsIkdyaWQiLCJCdXR0b24iLCJDZW50ZXIiLCJDYXJkIiwiVGl0bGUiLCJRUkNvZGVGb3JtIiwiUVJDb2RlRGlzcGxheSIsIlFSQ29kZUhpc3RvcnkiLCJ1c2VBbmFseXRpY3MiLCJMT0NBTF9TVE9SQUdFX0tFWSIsIk1BWF9ISVNUT1JZX0VOVFJJRVMiLCJERUZBVUxUX1VSTCIsIkRFRkFVTFRfRE9UX1RZUEUiLCJERUZBVUxUX0RPVF9DT0xPUiIsIkRFRkFVTFRfQkFDS0dST1VORF9DT0xPUiIsImxvYWRIaXN0b3J5Iiwic3RvcmVkSGlzdG9yeSIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJKU09OIiwicGFyc2UiLCJlcnJvciIsImNvbnNvbGUiLCJnZXRJbml0aWFsUGFyYW1zIiwidXJsUGFyYW1zIiwiVVJMU2VhcmNoUGFyYW1zIiwid2luZG93IiwibG9jYXRpb24iLCJzZWFyY2giLCJjb25maWdQYXJhbSIsImdldCIsImRlY29kZWRDb25maWciLCJhdG9iIiwiUVJDb2RlR2VuZXJhdG9yUGFnZSIsInVybCIsImluaXRpYWxVcmwiLCJkb3RUeXBlIiwiaW5pdGlhbERvdFR5cGUiLCJkb3RDb2xvciIsImluaXRpYWxEb3RDb2xvciIsImJhY2tncm91bmRDb2xvciIsImluaXRpYWxCYWNrZ3JvdW5kQ29sb3IiLCJzZXRVcmwiLCJzZXREb3RUeXBlIiwic2V0RG90Q29sb3IiLCJzZXRCYWNrZ3JvdW5kQ29sb3IiLCJoaXN0b3J5Iiwic2V0SGlzdG9yeSIsInFyQ29kZVJlZiIsInRyYWNrUVJDb2RlR2VuZXJhdGlvbiIsInRyYWNrRW5nYWdlbWVudCIsInRyYWNrUVJDb2RlRG93bmxvYWQiLCJzYXZlSGlzdG9yeSIsIm5ld0hpc3RvcnkiLCJzZXRJdGVtIiwic3RyaW5naWZ5IiwiY29uZmlnIiwiaXNEZWZhdWx0Q29uZmlnIiwiZGVsZXRlIiwiZW5jb2RlZENvbmZpZyIsImJ0b2EiLCJzZXQiLCJyZXBsYWNlU3RhdGUiLCJwYXRobmFtZSIsImhhbmRsZUdlbmVyYXRlUVJDb2RlIiwidmFsdWVzIiwibmV3RW50cnkiLCJ0aW1lc3RhbXAiLCJEYXRlIiwibm93IiwicHJldkhpc3RvcnkiLCJ1cGRhdGVkSGlzdG9yeSIsInNsaWNlIiwiaGFuZGxlTG9hZEhpc3RvcnlFbnRyeSIsImVudHJ5IiwiaGFuZGxlRGVsZXRlSGlzdG9yeUVudHJ5IiwiaW5kZXgiLCJmaWx0ZXIiLCJfIiwiaSIsImdhcCIsIm9yZGVyIiwiZ3V0dGVyIiwiQ29sIiwic3BhbiIsInNtIiwibWQiLCJsZyIsImJhc2UiLCJzaGFkb3ciLCJwYWRkaW5nIiwicmFkaXVzIiwid2l0aEJvcmRlciIsIm9uR2VuZXJhdGUiLCJvbkRvdFR5cGVDaGFuZ2UiLCJvbkRvdENvbG9yQ2hhbmdlIiwib25CYWNrZ3JvdW5kQ29sb3JDaGFuZ2UiLCJzdHlsZSIsImZsZXhHcm93Iiwic2l6ZSIsInZhcmlhbnQiLCJvbkNsaWNrIiwib3BlbiIsImN1cnJlbnQiLCJkb3dubG9hZCIsIm5hbWUiLCJleHRlbnNpb24iLCJjb2xvciIsImgiLCJkYXRhIiwidyIsIm9uTG9hZEhpc3RvcnlFbnRyeSIsIm9uRGVsZXRlSGlzdG9yeUVudHJ5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/QRCodeGeneratorPage.tsx\n"));

/***/ })

});